{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15221872889701672926, "path": 10052607232589275058, "deps": [[5820056977320921005, "anstream", false, 6239206696183828129], [9394696648929125047, "anstyle", false, 15919516909729918110], [11166530783118767604, "strsim", false, 11948167205136221230], [11649982696571033535, "clap_lex", false, 10384310256762417728]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-e27ffbad6839fa7f\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}