{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5359720273228040123, "build_script_build", false, 4924442644011602911], [4885725550624711673, "build_script_build", false, 2051083220748529656], [1696678251960462246, "build_script_build", false, 9454727650214034728]], "local": [{"RerunIfEnvChanged": {"var": "LLVM_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBCLANG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBCLANG_STATIC_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS_x86_64_pc_windows_msvc", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}