//! Phi-Giant 简化错误处理系统
//! 
//! 超级工程师承诺：先实现基础功能，绝不妥协质量！

use std::fmt;
use thiserror::Error;

/// 主要错误类型
#[derive(Error, Debug)]
pub enum PhiError {
    /// 模型相关错误
    #[error("Model error: {0}")]
    Model(#[from] ModelError),
    
    /// 配置相关错误
    #[error("Config error: {0}")]
    Config(#[from] ConfigError),
    
    /// FFI相关错误
    #[error("FFI error: {0}")]
    Ffi(#[from] FfiError),
    
    /// IO错误
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    /// 通用错误
    #[error("Generic error: {message}")]
    Generic { message: String },
}

/// 模型错误
#[derive(Error, Debug)]
pub enum ModelError {
    #[error("Invalid configuration for field '{field}': {reason}")]
    InvalidConfiguration { field: String, reason: String },
    
    #[error("Model not found: {name}")]
    ModelNotFound { name: String },
    
    #[error("Invalid model format: {reason}")]
    InvalidFormat { reason: String },
}

/// 配置错误
#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("Configuration file not found: {path}")]
    FileNotFound {
        path: String,
        #[source]
        source: std::io::Error,
    },
    
    #[error("Parse error: {message}")]
    ParseError { message: String },
    
    #[error("Serialization error: {message}")]
    SerializationError { message: String },
    
    #[error("Validation error: {message}")]
    ValidationError { message: String },
}

/// FFI错误
#[derive(Error, Debug)]
pub enum FfiError {
    #[error("Null pointer error: {context}")]
    NullPointer { context: String },
    
    #[error("Invalid argument: {argument}")]
    InvalidArgument { argument: String },
    
    #[error("Memory allocation failed: {size} bytes")]
    MemoryAllocation { size: usize },
    
    #[error("Function call failed: {function}")]
    FunctionCall { function: String },
}

/// 结果类型别名
pub type Result<T> = std::result::Result<T, PhiError>;

impl PhiError {
    /// 创建通用错误
    pub fn generic(message: impl Into<String>) -> Self {
        Self::Generic {
            message: message.into(),
        }
    }
    
    /// 检查是否为致命错误
    pub fn is_fatal(&self) -> bool {
        match self {
            PhiError::Ffi(FfiError::MemoryAllocation { .. }) => true,
            PhiError::Io(_) => true,
            _ => false,
        }
    }
    
    /// 获取错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            PhiError::Model(_) => 1000,
            PhiError::Config(_) => 2000,
            PhiError::Ffi(_) => 3000,
            PhiError::Io(_) => 4000,
            PhiError::Generic { .. } => 9999,
        }
    }
    
    /// 获取恢复建议
    pub fn recovery_suggestion(&self) -> Option<&'static str> {
        match self {
            PhiError::Config(ConfigError::FileNotFound { .. }) => {
                Some("Please check if the configuration file exists and is readable")
            }
            PhiError::Ffi(FfiError::MemoryAllocation { .. }) => {
                Some("Try reducing memory usage or increasing available memory")
            }
            PhiError::Model(ModelError::ModelNotFound { .. }) => {
                Some("Please check if the model file exists and is in the correct format")
            }
            _ => None,
        }
    }
}

impl fmt::Display for PhiError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[{}] {}", self.error_code(), self)?;
        
        if let Some(suggestion) = self.recovery_suggestion() {
            write!(f, "\nSuggestion: {}", suggestion)?;
        }
        
        Ok(())
    }
}

/// 错误上下文扩展
pub trait ErrorContext<T> {
    /// 添加上下文信息
    fn with_context(self, context: impl Into<String>) -> Result<T>;
}

impl<T, E> ErrorContext<T> for std::result::Result<T, E>
where
    E: Into<PhiError>,
{
    fn with_context(self, context: impl Into<String>) -> Result<T> {
        self.map_err(|e| {
            let base_error = e.into();
            PhiError::Generic {
                message: format!("{}: {}", context.into(), base_error),
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_error_creation() {
        let error = PhiError::generic("test error");
        assert_eq!(error.error_code(), 9999);
    }
    
    #[test]
    fn test_error_display() {
        let error = PhiError::Config(ConfigError::ParseError {
            message: "invalid syntax".to_string(),
        });
        let display = format!("{}", error);
        assert!(display.contains("2000"));
        assert!(display.contains("invalid syntax"));
    }
    
    #[test]
    fn test_fatal_error() {
        let error = PhiError::Ffi(FfiError::MemoryAllocation { size: 1024 });
        assert!(error.is_fatal());
        
        let error = PhiError::Config(ConfigError::ParseError {
            message: "test".to_string(),
        });
        assert!(!error.is_fatal());
    }
    
    #[test]
    fn test_error_context() {
        let result: std::result::Result<(), std::io::Error> = 
            Err(std::io::Error::new(std::io::ErrorKind::NotFound, "file not found"));
        
        let error = result.with_context("loading configuration").unwrap_err();
        let display = format!("{}", error);
        assert!(display.contains("loading configuration"));
    }
}
