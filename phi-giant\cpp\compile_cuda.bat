@echo off
REM 超级工程师CUDA独立编译脚本
echo 🚀 Setting up Visual Studio environment...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

echo 🔧 Compiling CUDA kernels...
nvcc -O3 --use_fast_math --extended-lambda --expt-relaxed-constexpr -arch=sm_75 -c phi_cuda.cu -o phi_cuda_compiled.obj

if %ERRORLEVEL% EQU 0 (
    echo ✅ CUDA compilation successful!
    echo 📁 Output: phi_cuda_compiled.obj
) else (
    echo ❌ CUDA compilation failed!
    exit /b 1
)

echo 🎯 Testing CUDA runtime...
nvcc --version

echo 🚀 CUDA independent compilation completed!
