#pragma once
#include <vector>
#include <memory>
#include <cstring>
#include <stdexcept>
#include <cmath>
#include <algorithm>
#include <random>
#include <unordered_map>
#include <any>
#include <string>

#if defined(PHI_USE_CUDA) && PHI_USE_CUDA
#include <cuda_runtime.h>
#include <cuda_fp16.h>
#endif

namespace phi {

enum class DataType {
    FP32 = 0,
    FP16 = 1,
    FP8 = 2,
    INT8 = 3,
    INT16 = 4,
    INT32 = 5,
    INT4 = 6,
    UINT8 = 7
};

enum class Device {
    CPU = 0,
    CUDA = 1
};

class PhiTensor {
public:
    // 构造函数
    PhiTensor(const std::vector<size_t>& shape, DataType dtype = DataType::FP32,
              Device target_device = Device::CPU);
    
    // 移动构造函数
    PhiTensor(PhiTensor&& other) noexcept;
    
    // 拷贝构造函数
    PhiTensor(const PhiTensor& other);
    
    // 赋值操作符
    PhiTensor& operator=(const PhiTensor& other);
    PhiTensor& operator=(PhiTensor&& other) noexcept;
    
    // 析构函数
    ~PhiTensor();
    
    // 维度操作
    const std::vector<size_t>& shape() const { return shape_; }
    size_t ndim() const { return shape_.size(); }
    size_t size() const;
    size_t bytes() const;
    
    // 数据类型操作
    DataType dtype() const { return dtype_; }
    void convert_dtype(DataType new_dtype);
    
    // 设备操作
    Device device() const { return device_; }
    void to_device(Device new_device);
    
    // 数据访问
    template <typename T>
    T* data();
    
    template <typename T>
    const T* data() const;
    
    // 填充操作
    void fill(float value);
    void uniform(float low, float high);
    void normal(float mean, float std);
    void xavier_uniform(size_t fan_in, size_t fan_out);
    void kaiming_uniform(size_t fan_in);
    
    // 数学操作
    PhiTensor operator+(const PhiTensor& other) const;
    PhiTensor operator-(const PhiTensor& other) const;
    PhiTensor operator*(const PhiTensor& other) const;
    PhiTensor operator*(float scalar) const;
    
    // 激活函数
    PhiTensor relu() const;
    PhiTensor tanh() const;
    PhiTensor sigmoid() const;
    PhiTensor softmax(int dim = -1) const;
    
    // 统计操作
    float max() const;
    float min() const;
    float mean() const;
    float sum() const;
    PhiTensor abs() const;
    
    // 形状操作
    PhiTensor reshape(const std::vector<size_t>& new_shape) const;
    PhiTensor transpose(int dim0, int dim1) const;
    
    // 静态创建方法
    static PhiTensor zeros(const std::vector<size_t>& shape, DataType dtype = DataType::FP32,
                          Device target_device = Device::CPU);
    static PhiTensor ones(const std::vector<size_t>& shape, DataType dtype = DataType::FP32,
                         Device target_device = Device::CPU);
    static PhiTensor rand(const std::vector<size_t>& shape, DataType dtype = DataType::FP32,
                         Device target_device = Device::CPU);
    static PhiTensor constant(float value, const std::vector<size_t>& shape,
                             DataType dtype = DataType::FP32, Device target_device = Device::CPU);
    
    // Metadata operations (simplified)
    void set_name(const std::string& name);
    std::string get_name() const;
    
    // 内存同步（CUDA）
    void synchronize() const;
    
    // 调试信息
    void print_info() const;

private:
    std::vector<size_t> shape_;
    DataType dtype_;
    Device device_;
    void* data_ptr_;
    
    void allocate_memory();
    void free_memory();
    size_t calculate_bytes() const;
    size_t dtype_size() const;
    
    // 设备特定操作
    void* allocate_cpu(size_t bytes);
    void* allocate_cuda(size_t bytes);
    void free_cpu(void* ptr);
    void free_cuda(void* ptr);
    
    // 数据转换
    void convert_cpu(const void* src, void* dst, DataType src_dtype, DataType dst_dtype, size_t count);
    void convert_cuda(const void* src, void* dst, DataType src_dtype, DataType dst_dtype, size_t count);
    
    // 数学操作实现
    void add_cpu(const PhiTensor& other, PhiTensor& result) const;
    void add_cuda(const PhiTensor& other, PhiTensor& result) const;
    
    void mul_cpu(const PhiTensor& other, PhiTensor& result) const;
    void mul_cuda(const PhiTensor& other, PhiTensor& result) const;
    
    // 激活函数实现
    void relu_cpu(PhiTensor& result) const;
    void relu_cuda(PhiTensor& result) const;
    
    void tanh_cpu(PhiTensor& result) const;
    void tanh_cuda(PhiTensor& result) const;
    
    void sigmoid_cpu(PhiTensor& result) const;
    void sigmoid_cuda(PhiTensor& result) const;
    
    // 统计操作实现
    float reduce_max_cpu() const;
    float reduce_max_cuda() const;
    
    float reduce_sum_cpu() const;
    float reduce_sum_cuda() const;
    
    // 随机数生成器
    static std::mt19937& get_rng();
};

// 模板特化声明
template <>
float* PhiTensor::data<float>();

template <>
const float* PhiTensor::data<float>() const;

#if defined(PHI_USE_CUDA) && PHI_USE_CUDA
template <>
__half* PhiTensor::data<__half>();

template <>
const __half* PhiTensor::data<__half>() const;
#endif

template <>
int8_t* PhiTensor::data<int8_t>();

template <>
const int8_t* PhiTensor::data<int8_t>() const;

template <>
uint8_t* PhiTensor::data<uint8_t>();

template <>
const uint8_t* PhiTensor::data<uint8_t>() const;

// 工具函数
size_t get_dtype_size(DataType dtype);
std::string dtype_to_string(DataType dtype);
std::string device_to_string(Device device);

// 矩阵乘法
PhiTensor matmul(const PhiTensor& a, const PhiTensor& b);

// 卷积操作
PhiTensor conv2d(const PhiTensor& input, const PhiTensor& weight, 
                 const PhiTensor& bias, int stride = 1, int padding = 0);

} // namespace phi
