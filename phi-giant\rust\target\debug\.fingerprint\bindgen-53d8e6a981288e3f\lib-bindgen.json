{"rustc": 1842507548689473721, "features": "[\"default\", \"logging\", \"prettyplease\", \"runtime\", \"which-rustfmt\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_5\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 15460903241111225995, "profile": 2225463790103693989, "path": 15565741480191581066, "deps": [[950716570147248582, "cexpr", false, 4819382091917086415], [1696678251960462246, "prettyplease", false, 16563295781821335120], [2004958070545769120, "lazycell", false, 1994501435364064547], [3060637413840920116, "proc_macro2", false, 9742792036988073286], [4885725550624711673, "clang_sys", false, 5133958054819548063], [4974441333307933176, "syn", false, 17297303451864429965], [5359720273228040123, "build_script_build", false, 5560257324382614433], [5986029879202738730, "log", false, 16544253528419911208], [6243494903393190189, "which", false, 2870540982306821394], [7896293946984509699, "bitflags", false, 10835267859002908756], [8410525223747752176, "shlex", false, 4852131475975642242], [9451456094439810778, "regex", false, 88200075924095248], [11863159202453368486, "peeking_take_while", false, 1176672226460868850], [16055916053474393816, "rustc_hash", false, 7644518541633815283], [17917672826516349275, "lazy_static", false, 15670739947279810315], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-53d8e6a981288e3f\\dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}