{"rustc": 1842507548689473721, "features": "[\"default\", \"quantization\"]", "declared_features": "[\"criterion\", \"cuda\", \"cudarc\", \"default\", \"dev\", \"js-sys\", \"profiling\", \"quantization\", \"wasm\", \"wasm-bindgen\", \"web-sys\"]", "target": 5408242616063297496, "profile": 7409704062750675268, "path": 13767053534773805487, "deps": [[3214373357989284387, "pkg_config", false, 13252006675240338182], [5359720273228040123, "bindgen", false, 2669629791583133015], [7499741813737603141, "cmake", false, 10369070915430281967], [15056754423999335055, "cc", false, 12231068117531539533]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\phi-giant-25f8ee5458c528dc\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}