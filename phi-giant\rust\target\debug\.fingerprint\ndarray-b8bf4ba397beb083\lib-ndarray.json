{"rustc": 1842507548689473721, "features": "[\"default\", \"rayon\", \"rayon_\", \"serde\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 15657897354478470176, "path": 2525462169438355074, "deps": [[5157631553186200874, "num_traits", false, 522175165424791702], [9689903380558560274, "serde", false, 8600844104371067103], [10697383615564341592, "rayon_", false, 3329352285143233302], [12319020793864570031, "num_complex", false, 7755965916938414202], [15709748443193639506, "rawpointer", false, 13738660862367348416], [15826188163127377936, "matrixmultiply", false, 3376436245802699118], [16795989132585092538, "num_integer", false, 7278081414431634751]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ndarray-b8bf4ba397beb083\\dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}