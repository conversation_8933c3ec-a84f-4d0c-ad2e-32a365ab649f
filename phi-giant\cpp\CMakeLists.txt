cmake_minimum_required(VERSION 3.18)

# 🚀 超级工程师智能CUDA策略 - 适应性GPU加速方案
# 检测CUDA工具包
set(CUDA_TOOLKIT_ROOT_DIR "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8")
set(NVCC_COMPILER "${CUDA_TOOLKIT_ROOT_DIR}/bin/nvcc.exe")

# 检查CUDA是否可用
if(EXISTS "${NVCC_COMPILER}")
    # 尝试启用CUDA
    include(CheckLanguage)
    check_language(CUDA)

    if(CMAKE_CUDA_COMPILER)
        enable_language(CUDA)
        project(PhiGiant LANGUAGES CXX CUDA)
        set(CUDA_AVAILABLE TRUE)
        set(CMAKE_CUDA_ARCHITECTURES "70;75;80;86;89;90")
        message(STATUS "🎯 CUDA successfully enabled: ${CMAKE_CUDA_COMPILER}")
        message(STATUS "🚀 GPU acceleration available!")
    else()
        # CUDA工具包存在但CMake无法启用 - 准备独立编译
        project(PhiGiant LANGUAGES CXX)
        set(CUDA_AVAILABLE FALSE)
        set(CUDA_INDEPENDENT_BUILD TRUE)
        message(STATUS "⚠️ CUDA toolkit found but CMake integration failed")
        message(STATUS "🔧 Will use independent CUDA compilation")
        message(STATUS "🔥 Building EXTREME CPU-optimized version with CUDA support!")
    endif()
else()
    # 没有CUDA工具包
    project(PhiGiant LANGUAGES CXX)
    set(CUDA_AVAILABLE FALSE)
    set(CUDA_INDEPENDENT_BUILD FALSE)
    message(STATUS "ℹ️ CUDA toolkit not found")
    message(STATUS "🔥 Building EXTREME CPU-optimized version!")
endif()

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /O2 /fp:fast")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -ffast-math")
endif()

# 🚀 超级工程师CUDA配置 - 智能适应方案
if(CUDA_AVAILABLE)
    # 设置CUDA编译器标志 - 极致性能优化
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -O3 --use_fast_math")
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} --extended-lambda")
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} --expt-relaxed-constexpr")
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} --expt-extended-lambda")
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -Xcompiler /bigobj")

    # 设置CUDA分离编译
    set(CMAKE_CUDA_SEPARABLE_COMPILATION ON)
    set(CMAKE_CUDA_RESOLVE_DEVICE_SYMBOLS ON)

    # 查找CUDA工具包
    find_package(CUDAToolkit REQUIRED)
    if(CUDAToolkit_FOUND)
        set(CUDA_LIBRARIES_FOUND TRUE)
        message(STATUS "✅ CUDA Toolkit found: ${CUDAToolkit_VERSION}")
    endif()

    message(STATUS "🎯 CUDA Configuration:")
    message(STATUS "   CUDA Compiler: ${CMAKE_CUDA_COMPILER}")
    message(STATUS "   CUDA Architectures: ${CMAKE_CUDA_ARCHITECTURES}")

elseif(CUDA_INDEPENDENT_BUILD)
    # 独立CUDA编译配置
    message(STATUS "🔧 Setting up independent CUDA compilation")
    set(CUDA_NVCC_EXECUTABLE "${NVCC_COMPILER}")
    set(CUDA_LIBRARIES_FOUND FALSE)

    # 添加自定义CUDA编译规则
    set(CUDA_COMPILE_FLAGS "-O3 --use_fast_math --extended-lambda --expt-relaxed-constexpr")
    set(CUDA_ARCHITECTURES "70;75;80;86;89;90")

    message(STATUS "🎯 Independent CUDA Configuration:")
    message(STATUS "   NVCC Executable: ${CUDA_NVCC_EXECUTABLE}")
    message(STATUS "   CUDA Architectures: ${CUDA_ARCHITECTURES}")
endif()

# 查找其他依赖
find_package(Threads REQUIRED)

# 配置OpenSSL路径 - 尝试多个可能的位置
set(OPENSSL_ROOT_DIR "C:/Program Files/OpenSSL-Win64")
set(OPENSSL_INCLUDE_DIR "${OPENSSL_ROOT_DIR}/include")

# 尝试不同的库文件路径
if(EXISTS "${OPENSSL_ROOT_DIR}/lib/VC/x64/MT/libcrypto.lib")
    set(OPENSSL_CRYPTO_LIBRARY "${OPENSSL_ROOT_DIR}/lib/VC/x64/MT/libcrypto.lib")
    set(OPENSSL_SSL_LIBRARY "${OPENSSL_ROOT_DIR}/lib/VC/x64/MT/libssl.lib")
elseif(EXISTS "${OPENSSL_ROOT_DIR}/lib/VC/libcrypto64MT.lib")
    set(OPENSSL_CRYPTO_LIBRARY "${OPENSSL_ROOT_DIR}/lib/VC/libcrypto64MT.lib")
    set(OPENSSL_SSL_LIBRARY "${OPENSSL_ROOT_DIR}/lib/VC/libssl64MT.lib")
elseif(EXISTS "${OPENSSL_ROOT_DIR}/lib/libcrypto.lib")
    set(OPENSSL_CRYPTO_LIBRARY "${OPENSSL_ROOT_DIR}/lib/libcrypto.lib")
    set(OPENSSL_SSL_LIBRARY "${OPENSSL_ROOT_DIR}/lib/libssl.lib")
else()
    message(STATUS "Searching for OpenSSL libraries...")
    file(GLOB_RECURSE CRYPTO_LIBS "${OPENSSL_ROOT_DIR}/lib/*crypto*.lib")
    file(GLOB_RECURSE SSL_LIBS "${OPENSSL_ROOT_DIR}/lib/*ssl*.lib")
    if(CRYPTO_LIBS)
        list(GET CRYPTO_LIBS 0 OPENSSL_CRYPTO_LIBRARY)
        message(STATUS "Found crypto library: ${OPENSSL_CRYPTO_LIBRARY}")
    endif()
    if(SSL_LIBS)
        list(GET SSL_LIBS 0 OPENSSL_SSL_LIBRARY)
        message(STATUS "Found SSL library: ${OPENSSL_SSL_LIBRARY}")
    endif()
endif()

find_package(OpenSSL REQUIRED)

if(OpenSSL_FOUND)
    message(STATUS "✅ OpenSSL found at: ${OPENSSL_ROOT_DIR}")
    message(STATUS "✅ OpenSSL include: ${OPENSSL_INCLUDE_DIR}")
    message(STATUS "✅ OpenSSL crypto: ${OPENSSL_CRYPTO_LIBRARY}")
    message(STATUS "✅ OpenSSL SSL: ${OPENSSL_SSL_LIBRARY}")
    set(PHI_USE_OPENSSL ON)
else()
    message(FATAL_ERROR "❌ OpenSSL is required for enterprise features!")
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 源文件
set(PHI_SOURCES
    phi_tensor_simple.cpp
    phi_ffi_simple_impl.cpp
    phi_enterprise_minimal.cpp
)

# 企业级功能头文件
set(PHI_ENTERPRISE_HEADERS
    phi_security.hpp
    phi_performance.hpp
    phi_testing.hpp
    phi_tensor_simple.hpp
    phi_neuron.hpp
    phi_quant.hpp
    phi_ffi_simple.hpp
)

# 检查源文件是否存在
foreach(source ${PHI_SOURCES})
    if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/${source}")
        message(WARNING "Source file ${source} not found, removing from build")
        list(REMOVE_ITEM PHI_SOURCES ${source})
    endif()
endforeach()

# CUDA源文件（仅在CUDA可用时）
if(CUDA_AVAILABLE)
    set(PHI_CUDA_SOURCES
        phi_cuda.cu
    )

    # 检查CUDA源文件是否存在
    foreach(source ${PHI_CUDA_SOURCES})
        if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/${source}")
            message(WARNING "CUDA source file ${source} not found, removing from build")
            list(REMOVE_ITEM PHI_CUDA_SOURCES ${source})
        endif()
    endforeach()
endif()

# 创建静态库
add_library(phi_core STATIC ${PHI_SOURCES})

# 编译定义
target_compile_definitions(phi_core PRIVATE
    PHI_VERSION_MAJOR=1
    PHI_VERSION_MINOR=0
    PHI_VERSION_PATCH=0
)

# 🚀 超级工程师CUDA库智能配置
if(CUDA_AVAILABLE AND PHI_CUDA_SOURCES)
    # 标准CUDA编译
    add_library(phi_cuda STATIC ${PHI_CUDA_SOURCES})

    # 设置CUDA属性
    set_target_properties(phi_cuda PROPERTIES
        CUDA_SEPARABLE_COMPILATION ON
        CUDA_RESOLVE_DEVICE_SYMBOLS ON
        CUDA_STANDARD 17
        CUDA_STANDARD_REQUIRED ON
        CUDA_ARCHITECTURES "${CMAKE_CUDA_ARCHITECTURES}"
    )

    # CUDA编译定义
    target_compile_definitions(phi_cuda PRIVATE
        PHI_USE_CUDA=1
        PHI_CUDA_OPTIMIZED=1
        PHI_CUDA_FUSED_KERNELS=1
    )

    target_compile_definitions(phi_core PRIVATE
        PHI_USE_CUDA=1
    )

    # 链接CUDA库
    if(CUDA_LIBRARIES_FOUND)
        target_link_libraries(phi_cuda
            CUDA::cublas
            CUDA::curand
            CUDA::cusparse
            CUDA::cudart
        )
    endif()

    target_link_libraries(phi_core phi_cuda)
    message(STATUS "🚀 CUDA acceleration enabled!")

elseif(CUDA_INDEPENDENT_BUILD AND PHI_CUDA_SOURCES)
    # 独立CUDA编译
    message(STATUS "🔧 Setting up independent CUDA compilation for ${PHI_CUDA_SOURCES}")

    # 创建CUDA对象文件列表
    set(CUDA_OBJECTS "")
    foreach(cuda_source ${PHI_CUDA_SOURCES})
        get_filename_component(cuda_name ${cuda_source} NAME_WE)
        set(cuda_object "${CMAKE_CURRENT_BINARY_DIR}/${cuda_name}.obj")
        list(APPEND CUDA_OBJECTS ${cuda_object})

        # 添加自定义命令编译CUDA文件
        add_custom_command(
            OUTPUT ${cuda_object}
            COMMAND ${CUDA_NVCC_EXECUTABLE} ${CUDA_COMPILE_FLAGS}
                    -arch=sm_75 -c ${CMAKE_CURRENT_SOURCE_DIR}/${cuda_source}
                    -o ${cuda_object}
            DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/${cuda_source}
            COMMENT "Compiling CUDA object ${cuda_object}"
        )
    endforeach()

    # 创建CUDA静态库
    add_custom_target(phi_cuda_objects DEPENDS ${CUDA_OBJECTS})
    add_library(phi_cuda STATIC ${CUDA_OBJECTS})
    add_dependencies(phi_cuda phi_cuda_objects)

    target_compile_definitions(phi_core PRIVATE
        PHI_USE_CUDA=1
        PHI_CUDA_INDEPENDENT=1
    )

    target_link_libraries(phi_core phi_cuda)
    message(STATUS "🔧 Independent CUDA compilation configured!")

else()
    # CPU-only版本
    message(STATUS "🔥 Building EXTREME CPU-optimized version")
    target_compile_definitions(phi_core PRIVATE
        PHI_USE_CUDA=0
    )
endif()

# 链接通用依赖
target_link_libraries(phi_core Threads::Threads)

# 链接OpenSSL（必需）
target_link_libraries(phi_core OpenSSL::SSL OpenSSL::Crypto)
target_compile_definitions(phi_core PRIVATE PHI_USE_OPENSSL=1)
target_include_directories(phi_core PRIVATE ${OPENSSL_INCLUDE_DIR})
message(STATUS "✅ OpenSSL linked successfully - Full enterprise security enabled")

# 安装规则
if(CUDA_AVAILABLE AND PHI_CUDA_SOURCES)
    install(TARGETS phi_core phi_cuda
        ARCHIVE DESTINATION lib
        LIBRARY DESTINATION lib
        RUNTIME DESTINATION bin
    )
else()
    install(TARGETS phi_core
        ARCHIVE DESTINATION lib
        LIBRARY DESTINATION lib
        RUNTIME DESTINATION bin
    )
endif()

install(FILES
    ${PHI_ENTERPRISE_HEADERS}
    DESTINATION include/phi
)

# 测试
option(BUILD_TESTS "Build tests" ON)
if(BUILD_TESTS)
    enable_testing()

    # 简单测试程序
    add_executable(test_phi test_phi.cpp)
    target_link_libraries(test_phi phi_core)

    # 内存安全测试程序
    add_executable(test_memory_safety_final test_memory_safety_final.cpp)
    target_link_libraries(test_memory_safety_final phi_core)

    # 真正的企业功能测试程序
    add_executable(test_enterprise_final test_enterprise_final.cpp)
    target_link_libraries(test_enterprise_final phi_core)

    # 第一周企业功能测试程序
    add_executable(test_enterprise_week1 test_enterprise_week1.cpp)
    target_link_libraries(test_enterprise_week1 phi_core)

    # CUDA完整测试程序
    if(CUDA_AVAILABLE)
        add_executable(test_cuda_complete test_cuda_complete.cpp)
        target_link_libraries(test_cuda_complete phi_core)
        if(CUDA_LIBRARIES_FOUND)
            target_link_libraries(test_cuda_complete CUDA::cublas CUDA::curand CUDA::cudart)
        endif()
    endif()

    # SIMD完整测试程序
    add_executable(test_simd_complete test_simd_complete.cpp)
    target_link_libraries(test_simd_complete phi_core)

    # 内存优化测试程序
    add_executable(test_memory_optimization test_memory_optimization.cpp)
    target_link_libraries(test_memory_optimization phi_core)

    # 设置SIMD编译选项
    if(MSVC)
        target_compile_options(test_simd_complete PRIVATE /arch:AVX2)
    else()
        target_compile_options(test_simd_complete PRIVATE -mavx2 -mfma)
        # 检测AVX512支持
        include(CheckCXXCompilerFlag)
        check_cxx_compiler_flag("-mavx512f" COMPILER_SUPPORTS_AVX512F)
        if(COMPILER_SUPPORTS_AVX512F)
            target_compile_options(test_simd_complete PRIVATE -mavx512f -mavx512dq -mavx512bw)
        endif()
    endif()

    add_test(NAME phi_core_test COMMAND test_phi)
    add_test(NAME phi_memory_safety_test COMMAND test_memory_safety_final)
    add_test(NAME phi_enterprise_final_test COMMAND test_enterprise_final)
    add_test(NAME phi_enterprise_week1_test COMMAND test_enterprise_week1)
    add_test(NAME phi_simd_complete_test COMMAND test_simd_complete)
    add_test(NAME phi_memory_optimization_test COMMAND test_memory_optimization)

    if(CUDA_AVAILABLE)
        add_test(NAME phi_cuda_complete_test COMMAND test_cuda_complete)
    endif()
endif()

# 基准测试
option(BUILD_BENCHMARKS "Build benchmarks" OFF)
if(BUILD_BENCHMARKS)
    add_subdirectory(benchmarks)
endif()

# 示例
option(BUILD_EXAMPLES "Build examples" OFF)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 打印配置信息
message(STATUS "PHI-GIANT Configuration:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  CUDA Standard: ${CMAKE_CUDA_STANDARD}")
message(STATUS "  CUDA Architectures: ${CMAKE_CUDA_ARCHITECTURES}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  CUDA Toolkit: ${CUDA_TOOLKIT_ROOT_DIR}")
message(STATUS "  cuBLAS: ${CUBLAS_LIBRARIES}")
message(STATUS "  cuRAND: ${CURAND_LIBRARIES}")
message(STATUS "  cuSPARSE: ${CUSPARSE_LIBRARIES}")
