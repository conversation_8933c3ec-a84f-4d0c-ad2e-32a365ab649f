{"rustc": 1842507548689473721, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15221872889701672926, "path": 16709420614825456043, "deps": [[1457576002496728321, "clap_derive", false, 9375201824708655696], [12790452388100355468, "clap_builder", false, 10091684332997262679]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-eb34344a5ad336c0\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}