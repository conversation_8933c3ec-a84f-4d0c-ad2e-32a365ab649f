{"rustc": 1842507548689473721, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 12553840366118088266, "deps": [[555019317135488525, "regex_automata", false, 13659939576758462745], [9408802513701742484, "regex_syntax", false, 5478079900391790314]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-e47ea9df4ad96696\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}