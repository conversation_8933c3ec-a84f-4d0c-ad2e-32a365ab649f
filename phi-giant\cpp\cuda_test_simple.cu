#include <cuda_runtime.h>
#include <iostream>

// Simple CUDA kernel for testing
__global__ void test_kernel(float* data, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        data[idx] = data[idx] * 2.0f;
    }
}

// Test function
extern "C" {
    int cuda_test_simple() {
        const int size = 1024;
        const int bytes = size * sizeof(float);
        
        // Allocate host memory
        float* h_data = new float[size];
        for (int i = 0; i < size; i++) {
            h_data[i] = static_cast<float>(i);
        }
        
        // Allocate device memory
        float* d_data;
        cudaError_t err = cudaMalloc(&d_data, bytes);
        if (err != cudaSuccess) {
            delete[] h_data;
            return -1;
        }
        
        // Copy to device
        err = cudaMemcpy(d_data, h_data, bytes, cudaMemcpyHostToDevice);
        if (err != cudaSuccess) {
            cudaFree(d_data);
            delete[] h_data;
            return -2;
        }
        
        // Launch kernel
        dim3 block(256);
        dim3 grid((size + block.x - 1) / block.x);
        test_kernel<<<grid, block>>>(d_data, size);
        
        // Check for kernel launch errors
        err = cudaGetLastError();
        if (err != cudaSuccess) {
            cudaFree(d_data);
            delete[] h_data;
            return -3;
        }
        
        // Wait for completion
        err = cudaDeviceSynchronize();
        if (err != cudaSuccess) {
            cudaFree(d_data);
            delete[] h_data;
            return -4;
        }
        
        // Copy back to host
        err = cudaMemcpy(h_data, d_data, bytes, cudaMemcpyDeviceToHost);
        if (err != cudaSuccess) {
            cudaFree(d_data);
            delete[] h_data;
            return -5;
        }
        
        // Verify results
        bool success = true;
        for (int i = 0; i < 10; i++) {  // Check first 10 elements
            float expected = static_cast<float>(i) * 2.0f;
            if (h_data[i] != expected) {
                success = false;
                break;
            }
        }
        
        // Cleanup
        cudaFree(d_data);
        delete[] h_data;
        
        return success ? 0 : -6;
    }
}
