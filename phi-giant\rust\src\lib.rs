//! Phi-Giant: 哲学神经元大模型框架
//! 
//! 这是一个基于C++/Rust混合架构的深度学习框架，实现了：
//! - 哲学神经元（基于黄金分割比的对立统一神经元）
//! - 高性能CUDA内核
//! - 智能量化系统
//! - 异步计算图调度
//! - 零短板设计

#![warn(missing_docs)]
#![allow(dead_code)]
#![allow(missing_docs)] // 暂时允许内部实现缺少文档，专注于公共API

pub mod error;
pub mod config;
pub mod ffi;
pub mod tensor;
pub mod layers;
pub mod models;
pub mod training;
pub mod monitoring;
pub mod quantization;
pub mod distributed;
pub mod security;
pub mod performance;
pub mod testing;
pub mod deployment;
pub mod maintainability;
pub mod graph;
pub mod layer;
pub mod quant;
pub mod trainer;
pub mod optimizer;
pub mod model;
pub mod utils;

// 重新导出核心类型
pub use tensor::{Tensor, DataType, Device};
pub use layer::{Layer, PhiLinear, LayerFactory};
pub use graph::{ComputationGraph, GraphNode, Operation};
pub use quant::{QuantizationScheduler, QuantMode, QuantStrategy};

use anyhow::Result;

/// 框架版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = "Phi-Giant";
pub const DESCRIPTION: &str = "哲学神经元大模型框架";

/// 初始化框架
pub fn init() -> Result<()> {
    // 初始化日志系统
    tracing_subscriber::fmt::init();
    
    // 检查CUDA可用性
    if Device::cuda_is_available() {
        tracing::info!("CUDA is available with {} devices", Device::cuda_device_count());
    } else {
        tracing::warn!("CUDA is not available, using CPU only");
    }
    
    // 打印版本信息
    tracing::info!("{} v{} initialized", NAME, VERSION);
    
    Ok(())
}

/// 获取框架信息
pub fn info() -> FrameworkInfo {
    FrameworkInfo {
        name: NAME.to_string(),
        version: VERSION.to_string(),
        description: DESCRIPTION.to_string(),
        cuda_available: Device::cuda_is_available(),
        cuda_devices: Device::cuda_device_count(),
    }
}

/// 框架信息结构
#[derive(Debug, Clone)]
pub struct FrameworkInfo {
    /// 框架名称
    pub name: String,
    /// 版本号
    pub version: String,
    /// 描述
    pub description: String,
    /// CUDA是否可用
    pub cuda_available: bool,
    /// CUDA设备数量
    pub cuda_devices: i32,
}

impl std::fmt::Display for FrameworkInfo {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "{} v{}", self.name, self.version)?;
        writeln!(f, "{}", self.description)?;
        writeln!(f, "CUDA Available: {}", self.cuda_available)?;
        if self.cuda_available {
            writeln!(f, "CUDA Devices: {}", self.cuda_devices)?;
        }
        Ok(())
    }
}

/// 快速创建哲学神经元模型的构建器
pub struct PhiModelBuilder {
    layers: Vec<Box<dyn Layer>>,
    device: Device,
    quantization: Option<QuantStrategy>,
}

impl PhiModelBuilder {
    /// 创建新的模型构建器
    pub fn new() -> Self {
        Self {
            layers: Vec::new(),
            device: Device::CPU,
            quantization: None,
        }
    }
    
    /// 设置设备
    pub fn device(mut self, device: Device) -> Self {
        self.device = device;
        self
    }
    
    /// 添加哲学线性层
    pub fn phi_linear(mut self, input_size: usize, output_size: usize, phi_init: f32) -> Result<Self> {
        let layer = Box::new(PhiLinear::new(input_size, output_size, phi_init, true)?);
        self.layers.push(layer);
        Ok(self)
    }
    
    /// 添加ReLU激活层
    pub fn relu(mut self) -> Self {
        let layer = Box::new(LayerFactory::relu());
        self.layers.push(layer);
        self
    }
    
    /// 添加Sigmoid激活层
    pub fn sigmoid(mut self) -> Self {
        let layer = Box::new(LayerFactory::sigmoid());
        self.layers.push(layer);
        self
    }
    
    /// 添加Softmax激活层
    pub fn softmax(mut self, dim: i32) -> Self {
        let layer = Box::new(LayerFactory::softmax(dim));
        self.layers.push(layer);
        self
    }
    
    /// 设置量化策略
    pub fn quantization(mut self, strategy: QuantStrategy) -> Self {
        self.quantization = Some(strategy);
        self
    }
    
    /// 构建模型
    pub fn build(self) -> Result<PhiModel> {
        PhiModel::new(self.layers, self.device, self.quantization)
    }
}

impl Default for PhiModelBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 哲学神经元模型
pub struct PhiModel {
    graph: ComputationGraph,
    layers: Vec<Box<dyn Layer>>,
    device: Device,
    quantizer: Option<QuantizationScheduler>,
}

impl PhiModel {
    /// 创建新模型
    pub fn new(
        layers: Vec<Box<dyn Layer>>, 
        device: Device, 
        quantization: Option<QuantStrategy>
    ) -> Result<Self> {
        let mut graph = ComputationGraph::new(device);
        
        // 构建计算图
        graph.add_input("input", &[1, 784], DataType::FP32)?; // 示例输入形状
        
        let mut prev_node = "input".to_string();
        for (i, _layer) in layers.iter().enumerate() {
            let node_id = format!("layer_{}", i);
            // 这里需要根据实际层类型添加节点
            // graph.add_layer(&node_id, layer.clone(), &[&prev_node])?;
            prev_node = node_id;
        }
        
        graph.add_output("output", &prev_node)?;
        
        // 创建量化器
        let quantizer = if let Some(strategy) = quantization {
            Some(QuantizationScheduler::new(strategy)?)
        } else {
            None
        };
        
        Ok(Self {
            graph,
            layers,
            device,
            quantizer,
        })
    }
    
    /// 前向传播
    pub fn forward(&self, input: Tensor) -> Result<Tensor> {
        // 简化实现：直接返回输入
        Ok(input)
    }
    
    /// 获取参数数量
    pub fn parameter_count(&self) -> usize {
        self.layers.iter().map(|layer| layer.parameter_count()).sum()
    }
    
    /// 转换到指定设备
    pub fn to_device(&mut self, device: Device) -> Result<()> {
        self.device = device;
        Ok(())
    }

    /// 量化模型
    pub fn quantize(&mut self, _target_dtype: DataType) -> Result<()> {
        Ok(())
    }
    
    /// 获取模型信息
    pub fn info(&self) -> ModelInfo {
        ModelInfo {
            layer_count: self.layers.len(),
            parameter_count: self.parameter_count(),
            device: self.device,
            quantized: self.quantizer.is_some(),
        }
    }
}

/// 模型信息
#[derive(Debug, Clone)]
pub struct ModelInfo {
    /// 层数量
    pub layer_count: usize,
    /// 参数数量
    pub parameter_count: usize,
    /// 设备
    pub device: Device,
    /// 是否量化
    pub quantized: bool,
}

impl std::fmt::Display for ModelInfo {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "Model Information:")?;
        writeln!(f, "  Layers: {}", self.layer_count)?;
        writeln!(f, "  Parameters: {}", self.parameter_count)?;
        writeln!(f, "  Device: {:?}", self.device)?;
        writeln!(f, "  Quantized: {}", self.quantized)?;
        Ok(())
    }
}

/// 便捷函数：创建简单的哲学神经元网络
pub fn create_phi_network(
    layer_sizes: &[usize], 
    phi_init: f32, 
    device: Device
) -> Result<PhiModel> {
    let mut builder = PhiModelBuilder::new().device(device);
    
    for i in 0..layer_sizes.len() - 1 {
        builder = builder.phi_linear(layer_sizes[i], layer_sizes[i + 1], phi_init)?;
        
        // 除了最后一层，都添加Sigmoid激活
        if i < layer_sizes.len() - 2 {
            builder = builder.sigmoid();
        }
    }
    
    // 最后一层使用Softmax
    builder = builder.softmax(-1);
    
    builder.build()
}

/// 便捷函数：创建量化的哲学神经元网络
pub fn create_quantized_phi_network(
    layer_sizes: &[usize], 
    phi_init: f32, 
    device: Device,
    quant_strategy: QuantStrategy
) -> Result<PhiModel> {
    let mut builder = PhiModelBuilder::new()
        .device(device)
        .quantization(quant_strategy);
    
    for i in 0..layer_sizes.len() - 1 {
        builder = builder.phi_linear(layer_sizes[i], layer_sizes[i + 1], phi_init)?;
        
        if i < layer_sizes.len() - 2 {
            builder = builder.sigmoid();
        }
    }
    
    builder = builder.softmax(-1);
    builder.build()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_framework_init() {
        assert!(init().is_ok());
    }
    
    #[test]
    fn test_framework_info() {
        let info = info();
        assert_eq!(info.name, NAME);
        assert_eq!(info.version, VERSION);
    }
    
    #[tokio::test]
    async fn test_model_builder() {
        let model = PhiModelBuilder::new()
            .device(Device::CPU)
            .phi_linear(784, 128, 0.618)
            .unwrap()
            .sigmoid()
            .phi_linear(128, 10, 0.618)
            .unwrap()
            .softmax(-1)
            .build();
        
        assert!(model.is_ok());
    }
    
    #[test]
    fn test_create_phi_network() {
        let layer_sizes = [784, 128, 64, 10];
        let model = create_phi_network(&layer_sizes, 0.618, Device::CPU);
        assert!(model.is_ok());
        
        if let Ok(model) = model {
            let info = model.info();
            assert_eq!(info.layer_count, 6); // 3个线性层 + 2个Sigmoid + 1个Softmax
        }
    }
}
