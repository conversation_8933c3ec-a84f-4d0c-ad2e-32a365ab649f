use phi_giant::ffi_new::*;

#[test]
fn test_cuda_availability() {
    // This should not crash
    let available = cuda_is_available();
    let count = cuda_device_count();
    
    println!("CUDA available: {}", available);
    println!("CUDA device count: {}", count);
    
    // These calls should succeed regardless of CUDA availability
    assert!(count >= 0);
}

#[test]
fn test_simd_operations() {
    let a = vec![1.0, 2.0, 3.0, 4.0];
    let b = vec![5.0, 6.0, 7.0, 8.0];
    
    // Test dot product
    let dot = simd_dot_product(&a, &b).unwrap();
    println!("Dot product: {}", dot);
    assert!((dot - 70.0).abs() < 1e-6); // 1*5 + 2*6 + 3*7 + 4*8 = 70
    
    // Test vector addition
    let result = simd_add_vectors(&a, &b).unwrap();
    println!("Vector addition: {:?}", result);
    assert_eq!(result, vec![6.0, 8.0, 10.0, 12.0]);
}

#[test]
fn test_tensor_operations() {
    let data = vec![1.0, 2.0, 3.0, 4.0];
    let shape = vec![2, 2];
    
    let tensor = SafeTensor::new(&data, &shape).unwrap();
    println!("Tensor shape: {:?}", tensor.shape());
    println!("Tensor size: {}", tensor.size());
    println!("Tensor ndim: {}", tensor.ndim());
    
    assert_eq!(tensor.shape(), vec![2, 2]);
    assert_eq!(tensor.size(), 4);
    assert_eq!(tensor.ndim(), 2);
    
    let tensor_data = tensor.data();
    println!("Tensor data: {:?}", tensor_data);
    assert_eq!(tensor_data, &[1.0, 2.0, 3.0, 4.0]);
    
    // Test ReLU
    let relu_result = tensor.relu().unwrap();
    println!("ReLU result: {:?}", relu_result.data());
    assert_eq!(relu_result.data(), &[1.0, 2.0, 3.0, 4.0]); // All positive, no change
}

#[test]
fn test_tensor_creation() {
    // Test zeros tensor
    let zeros = SafeTensor::zeros(&[3, 3]).unwrap();
    println!("Zeros tensor: {:?}", zeros.data());
    assert_eq!(zeros.data(), &[0.0; 9]);
    
    // Test ones tensor
    let ones = SafeTensor::ones(&[2, 3]).unwrap();
    println!("Ones tensor: {:?}", ones.data());
    assert_eq!(ones.data(), &[1.0; 6]);
}

#[test]
fn test_tensor_arithmetic() {
    let data_a = vec![1.0, 2.0, 3.0, 4.0];
    let data_b = vec![5.0, 6.0, 7.0, 8.0];
    let shape = vec![2, 2];
    
    let tensor_a = SafeTensor::new(&data_a, &shape).unwrap();
    let tensor_b = SafeTensor::new(&data_b, &shape).unwrap();
    
    // Test addition
    let sum = tensor_a.add(&tensor_b).unwrap();
    println!("Tensor addition: {:?}", sum.data());
    assert_eq!(sum.data(), &[6.0, 8.0, 10.0, 12.0]);
    
    // Test multiplication
    let product = tensor_a.mul(&tensor_b).unwrap();
    println!("Tensor multiplication: {:?}", product.data());
    assert_eq!(product.data(), &[5.0, 12.0, 21.0, 32.0]);
}

#[test]
fn test_activation_functions() {
    let data = vec![-2.0, -1.0, 0.0, 1.0, 2.0];
    let shape = vec![5];
    
    let tensor = SafeTensor::new(&data, &shape).unwrap();
    
    // Test ReLU
    let relu_result = tensor.relu().unwrap();
    println!("ReLU(-2,-1,0,1,2): {:?}", relu_result.data());
    assert_eq!(relu_result.data(), &[0.0, 0.0, 0.0, 1.0, 2.0]);
    
    // Test Sigmoid
    let sigmoid_result = tensor.sigmoid().unwrap();
    println!("Sigmoid(-2,-1,0,1,2): {:?}", sigmoid_result.data());
    
    // Sigmoid should be between 0 and 1
    for &val in sigmoid_result.data() {
        assert!(val >= 0.0 && val <= 1.0);
    }
    
    // Sigmoid(0) should be approximately 0.5
    assert!((sigmoid_result.data()[2] - 0.5).abs() < 0.01);
}

#[test]
fn test_error_handling() {
    // Test mismatched vector lengths
    let a = vec![1.0, 2.0];
    let b = vec![1.0, 2.0, 3.0];
    
    let result = simd_dot_product(&a, &b);
    assert!(result.is_err());
    
    let result = simd_add_vectors(&a, &b);
    assert!(result.is_err());
}

#[test]
fn test_large_tensors() {
    // Test with larger tensors to verify performance
    let size = 10000;
    let data: Vec<f32> = (0..size).map(|i| i as f32).collect();
    let shape = vec![size as i64];
    
    let tensor = SafeTensor::new(&data, &shape).unwrap();
    assert_eq!(tensor.size(), size);
    
    // Test ReLU on large tensor
    let relu_result = tensor.relu().unwrap();
    assert_eq!(relu_result.size(), size);
    
    // All values should be non-negative (they already were)
    for &val in relu_result.data() {
        assert!(val >= 0.0);
    }
}
