E:\an\phi-giant\rust\target\debug\deps\phi_giant-edce4d5ea175c54d.d: src\lib.rs src\error.rs src\config.rs src\ffi.rs src\ffi_unified.rs src\ffi_zero_copy.rs src\ffi_manager.rs src\tensor.rs src\layers\mod.rs src\layers\linear.rs src\layers\activation.rs src\layers\normalization.rs src\layers\attention.rs src\models\mod.rs src\models\transformer.rs src\training\mod.rs src\monitoring\mod.rs src\monitoring\health.rs src\monitoring\tracing.rs src\quantization\mod.rs src\distributed\mod.rs src\security\mod.rs src\performance\mod.rs src\testing\mod.rs src\deployment\mod.rs src\maintainability\mod.rs src\graph.rs src\layer.rs src\quant.rs src\trainer.rs src\optimizer.rs src\model.rs src\utils.rs

E:\an\phi-giant\rust\target\debug\deps\libphi_giant-edce4d5ea175c54d.rmeta: src\lib.rs src\error.rs src\config.rs src\ffi.rs src\ffi_unified.rs src\ffi_zero_copy.rs src\ffi_manager.rs src\tensor.rs src\layers\mod.rs src\layers\linear.rs src\layers\activation.rs src\layers\normalization.rs src\layers\attention.rs src\models\mod.rs src\models\transformer.rs src\training\mod.rs src\monitoring\mod.rs src\monitoring\health.rs src\monitoring\tracing.rs src\quantization\mod.rs src\distributed\mod.rs src\security\mod.rs src\performance\mod.rs src\testing\mod.rs src\deployment\mod.rs src\maintainability\mod.rs src\graph.rs src\layer.rs src\quant.rs src\trainer.rs src\optimizer.rs src\model.rs src\utils.rs

src\lib.rs:
src\error.rs:
src\config.rs:
src\ffi.rs:
src\ffi_unified.rs:
src\ffi_zero_copy.rs:
src\ffi_manager.rs:
src\tensor.rs:
src\layers\mod.rs:
src\layers\linear.rs:
src\layers\activation.rs:
src\layers\normalization.rs:
src\layers\attention.rs:
src\models\mod.rs:
src\models\transformer.rs:
src\training\mod.rs:
src\monitoring\mod.rs:
src\monitoring\health.rs:
src\monitoring\tracing.rs:
src\quantization\mod.rs:
src\distributed\mod.rs:
src\security\mod.rs:
src\performance\mod.rs:
src\testing\mod.rs:
src\deployment\mod.rs:
src\maintainability\mod.rs:
src\graph.rs:
src\layer.rs:
src\quant.rs:
src\trainer.rs:
src\optimizer.rs:
src\model.rs:
src\utils.rs:

# env-dep:CARGO_PKG_NAME=phi-giant
# env-dep:CARGO_PKG_VERSION=1.0.0
