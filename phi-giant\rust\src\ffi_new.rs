//! FFI bindings for Phi-Giant C++ core library
//! 
//! This module provides safe Rust bindings to the C++ core library.

#![allow(non_snake_case)]
#![allow(non_camel_case_types)]
#![allow(dead_code)]

use std::os::raw::{c_int, c_float, c_char};
use std::ffi::CStr;

/// Error codes from C++ library
#[repr(C)]
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum PhiErrorCode {
    Success = 0,
    InvalidArgument = 1,
    OutOfMemory = 2,
    CudaError = 3,
    Unknown = 4,
}

/// Device types
#[repr(C)]
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum PhiDeviceType {
    CPU = 0,
    CUDA = 1,
}

/// Opaque tensor handle
#[repr(C)]
pub struct PhiTensor {
    _private: [u8; 0],
}

/// Opaque quantization manager handle
#[repr(C)]
pub struct PhiQuantManager {
    _private: [u8; 0],
}

/// Performance statistics
#[repr(C)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct PhiPerformanceStats {
    pub total_time_ms: f64,
    pub compute_time_ms: f64,
    pub memory_time_ms: f64,
    pub memory_usage_bytes: usize,
    pub peak_memory_bytes: usize,
}

extern "C" {
    // Error handling
    pub fn phi_get_last_error() -> *const c_char;
    
    // Tensor creation and destruction
    pub fn phi_tensor_create(data: *const c_float, shape: *const i64, ndim: usize) -> *mut PhiTensor;
    pub fn phi_tensor_zeros(shape: *const i64, ndim: usize) -> *mut PhiTensor;
    pub fn phi_tensor_ones(shape: *const i64, ndim: usize) -> *mut PhiTensor;
    pub fn phi_tensor_destroy(tensor: *mut PhiTensor);
    
    // Tensor properties
    pub fn phi_tensor_ndim(tensor: *const PhiTensor) -> usize;
    pub fn phi_tensor_shape(tensor: *const PhiTensor) -> *const i64;
    pub fn phi_tensor_size(tensor: *const PhiTensor) -> usize;
    pub fn phi_tensor_data(tensor: *mut PhiTensor) -> *mut c_float;
    pub fn phi_tensor_data_const(tensor: *const PhiTensor) -> *const c_float;
    
    // Tensor operations
    pub fn phi_tensor_add(a: *const PhiTensor, b: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_tensor_sub(a: *const PhiTensor, b: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_tensor_mul(a: *const PhiTensor, b: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_tensor_div(a: *const PhiTensor, b: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_tensor_matmul(a: *const PhiTensor, b: *const PhiTensor) -> *mut PhiTensor;
    
    // Activation functions
    pub fn phi_tensor_relu(tensor: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_tensor_sigmoid(tensor: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_tensor_tanh(tensor: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_tensor_softmax(tensor: *const PhiTensor) -> *mut PhiTensor;
    
    // Device operations
    pub fn phi_tensor_to_device(tensor: *const PhiTensor, device: PhiDeviceType) -> *mut PhiTensor;
    pub fn phi_tensor_convert_dtype(tensor: *const PhiTensor, dtype: c_int) -> *mut PhiTensor;
    
    // CUDA operations
    pub fn phi_cuda_is_available() -> bool;
    pub fn phi_cuda_device_count() -> c_int;
    pub fn phi_cuda_set_device(device: c_int);
    pub fn phi_cuda_get_device() -> c_int;
    
    // SIMD operations
    pub fn phi_simd_add_f32(a: *const c_float, b: *const c_float, result: *mut c_float, size: usize);
    pub fn phi_simd_mul_f32(a: *const c_float, b: *const c_float, result: *mut c_float, size: usize);
    pub fn phi_simd_dot_f32(a: *const c_float, b: *const c_float, size: usize) -> c_float;
    pub fn phi_simd_relu_f32(input: *const c_float, output: *mut c_float, size: usize);
    
    // Quantization operations
    pub fn phi_quant_manager_create() -> *mut PhiQuantManager;
    pub fn phi_quant_manager_destroy(manager: *mut PhiQuantManager);
    pub fn phi_quant_quantize_int8(manager: *mut PhiQuantManager, tensor: *const PhiTensor) -> *mut PhiTensor;
    pub fn phi_quant_dequantize_int8(manager: *mut PhiQuantManager, tensor: *const PhiTensor) -> *mut PhiTensor;
    
    // Performance monitoring
    pub fn phi_performance_start();
    pub fn phi_performance_get_stats() -> PhiPerformanceStats;
    pub fn phi_performance_reset();
}

/// Safe wrapper for getting last error
pub fn get_last_error_string() -> String {
    unsafe {
        let ptr = phi_get_last_error();
        if ptr.is_null() {
            return "No error".to_string();
        }
        
        match CStr::from_ptr(ptr).to_str() {
            Ok(s) => s.to_string(),
            Err(_) => "Invalid UTF-8 in error message".to_string(),
        }
    }
}

/// Safe wrapper for CUDA availability check
pub fn cuda_is_available() -> bool {
    unsafe { phi_cuda_is_available() }
}

/// Safe wrapper for CUDA device count
pub fn cuda_device_count() -> i32 {
    unsafe { phi_cuda_device_count() }
}

/// Safe wrapper for SIMD dot product
pub fn simd_dot_product(a: &[f32], b: &[f32]) -> Result<f32, String> {
    if a.len() != b.len() {
        return Err("Vector lengths must match".to_string());
    }
    
    if a.is_empty() {
        return Ok(0.0);
    }
    
    unsafe {
        Ok(phi_simd_dot_f32(a.as_ptr(), b.as_ptr(), a.len()))
    }
}

/// Safe wrapper for SIMD vector addition
pub fn simd_add_vectors(a: &[f32], b: &[f32]) -> Result<Vec<f32>, String> {
    if a.len() != b.len() {
        return Err("Vector lengths must match".to_string());
    }
    
    let mut result = vec![0.0f32; a.len()];
    
    unsafe {
        phi_simd_add_f32(a.as_ptr(), b.as_ptr(), result.as_mut_ptr(), a.len());
    }
    
    Ok(result)
}

/// Safe tensor wrapper
pub struct SafeTensor {
    ptr: *mut PhiTensor,
}

impl SafeTensor {
    /// Create a new tensor from data and shape
    pub fn new(data: &[f32], shape: &[i64]) -> Result<Self, String> {
        let ptr = unsafe {
            phi_tensor_create(data.as_ptr(), shape.as_ptr(), shape.len())
        };
        
        if ptr.is_null() {
            Err(get_last_error_string())
        } else {
            Ok(SafeTensor { ptr })
        }
    }
    
    /// Create a tensor of zeros
    pub fn zeros(shape: &[i64]) -> Result<Self, String> {
        let ptr = unsafe {
            phi_tensor_zeros(shape.as_ptr(), shape.len())
        };
        
        if ptr.is_null() {
            Err(get_last_error_string())
        } else {
            Ok(SafeTensor { ptr })
        }
    }
    
    /// Create a tensor of ones
    pub fn ones(shape: &[i64]) -> Result<Self, String> {
        let ptr = unsafe {
            phi_tensor_ones(shape.as_ptr(), shape.len())
        };
        
        if ptr.is_null() {
            Err(get_last_error_string())
        } else {
            Ok(SafeTensor { ptr })
        }
    }
    
    /// Get tensor dimensions
    pub fn ndim(&self) -> usize {
        unsafe { phi_tensor_ndim(self.ptr) }
    }
    
    /// Get tensor shape
    pub fn shape(&self) -> Vec<i64> {
        let ndim = self.ndim();
        if ndim == 0 {
            return vec![];
        }
        
        unsafe {
            let shape_ptr = phi_tensor_shape(self.ptr);
            if shape_ptr.is_null() {
                return vec![];
            }
            
            std::slice::from_raw_parts(shape_ptr, ndim).to_vec()
        }
    }
    
    /// Get tensor size (total number of elements)
    pub fn size(&self) -> usize {
        unsafe { phi_tensor_size(self.ptr) }
    }
    
    /// Get tensor data as slice
    pub fn data(&self) -> &[f32] {
        let size = self.size();
        if size == 0 {
            return &[];
        }
        
        unsafe {
            let data_ptr = phi_tensor_data_const(self.ptr);
            if data_ptr.is_null() {
                return &[];
            }
            
            std::slice::from_raw_parts(data_ptr, size)
        }
    }
    
    /// Apply ReLU activation
    pub fn relu(&self) -> Result<SafeTensor, String> {
        let ptr = unsafe { phi_tensor_relu(self.ptr) };
        
        if ptr.is_null() {
            Err(get_last_error_string())
        } else {
            Ok(SafeTensor { ptr })
        }
    }
    
    /// Apply sigmoid activation
    pub fn sigmoid(&self) -> Result<SafeTensor, String> {
        let ptr = unsafe { phi_tensor_sigmoid(self.ptr) };
        
        if ptr.is_null() {
            Err(get_last_error_string())
        } else {
            Ok(SafeTensor { ptr })
        }
    }
    
    /// Add two tensors
    pub fn add(&self, other: &SafeTensor) -> Result<SafeTensor, String> {
        let ptr = unsafe { phi_tensor_add(self.ptr, other.ptr) };
        
        if ptr.is_null() {
            Err(get_last_error_string())
        } else {
            Ok(SafeTensor { ptr })
        }
    }
    
    /// Multiply two tensors element-wise
    pub fn mul(&self, other: &SafeTensor) -> Result<SafeTensor, String> {
        let ptr = unsafe { phi_tensor_mul(self.ptr, other.ptr) };
        
        if ptr.is_null() {
            Err(get_last_error_string())
        } else {
            Ok(SafeTensor { ptr })
        }
    }
}

impl Drop for SafeTensor {
    fn drop(&mut self) {
        if !self.ptr.is_null() {
            unsafe {
                phi_tensor_destroy(self.ptr);
            }
        }
    }
}

unsafe impl Send for SafeTensor {}
unsafe impl Sync for SafeTensor {}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_cuda_availability() {
        // This should not crash
        let _available = cuda_is_available();
        let _count = cuda_device_count();
    }
    
    #[test]
    fn test_simd_operations() {
        let a = vec![1.0, 2.0, 3.0, 4.0];
        let b = vec![5.0, 6.0, 7.0, 8.0];
        
        // Test dot product
        let dot = simd_dot_product(&a, &b).unwrap();
        assert!((dot - 70.0).abs() < 1e-6); // 1*5 + 2*6 + 3*7 + 4*8 = 70
        
        // Test vector addition
        let result = simd_add_vectors(&a, &b).unwrap();
        assert_eq!(result, vec![6.0, 8.0, 10.0, 12.0]);
    }
    
    #[test]
    fn test_tensor_operations() {
        let data = vec![1.0, 2.0, 3.0, 4.0];
        let shape = vec![2, 2];
        
        let tensor = SafeTensor::new(&data, &shape).unwrap();
        assert_eq!(tensor.shape(), vec![2, 2]);
        assert_eq!(tensor.size(), 4);
        assert_eq!(tensor.ndim(), 2);
        
        let tensor_data = tensor.data();
        assert_eq!(tensor_data, &[1.0, 2.0, 3.0, 4.0]);
        
        // Test ReLU
        let relu_result = tensor.relu().unwrap();
        assert_eq!(relu_result.data(), &[1.0, 2.0, 3.0, 4.0]); // All positive, no change
    }
}
