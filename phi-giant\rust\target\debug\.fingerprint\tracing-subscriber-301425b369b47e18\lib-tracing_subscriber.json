{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 7872416515397446104, "deps": [[1009387600818341822, "matchers", false, 11688915716789758552], [1017461770342116999, "sharded_slab", false, 2615967847273895791], [1359731229228270592, "thread_local", false, 12938873905138082263], [3424551429995674438, "tracing_core", false, 11687418414869453627], [3666196340704888985, "smallvec", false, 12183241476430787015], [3722963349756955755, "once_cell", false, 14521976164538839542], [8606274917505247608, "tracing", false, 1957248498813977913], [8614575489689151157, "nu_ansi_term", false, 3861977783723061859], [9451456094439810778, "regex", false, 14340225900613074895], [10806489435541507125, "tracing_log", false, 16279463582921886815]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-301425b369b47e18\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}