#pragma once

#include <immintrin.h>
#include <string>
#include <cstring>
#include <algorithm>

/**
 * 真正高效的SIMD实现
 * 解决性能不佳的问题
 */

namespace phi {
namespace simd {

/**
 * 高度优化的向量操作类
 */
class OptimizedVectorOps {
public:
    // 内存对齐的向量加法 - 真正的高性能实现
    static void add_f32_aligned(const float* __restrict a, const float* __restrict b, 
                               float* __restrict result, size_t size) {
#if defined(__AVX2__)
        const size_t simd_size = size & ~7ULL; // 8的倍数
        
        // AVX2向量化循环 - 每次处理8个float
        for (size_t i = 0; i < simd_size; i += 8) {
            __m256 va = _mm256_load_ps(&a[i]);
            __m256 vb = _mm256_load_ps(&b[i]);
            __m256 vr = _mm256_add_ps(va, vb);
            _mm256_store_ps(&result[i], vr);
        }
        
        // 处理剩余元素
        for (size_t i = simd_size; i < size; ++i) {
            result[i] = a[i] + b[i];
        }
#else
        for (size_t i = 0; i < size; ++i) {
            result[i] = a[i] + b[i];
        }
#endif
    }
    
    // 非对齐的向量加法 - 使用loadu/storeu
    static void add_f32_unaligned(const float* __restrict a, const float* __restrict b, 
                                 float* __restrict result, size_t size) {
#if defined(__AVX2__)
        const size_t simd_size = size & ~7ULL;
        
        for (size_t i = 0; i < simd_size; i += 8) {
            __m256 va = _mm256_loadu_ps(&a[i]);
            __m256 vb = _mm256_loadu_ps(&b[i]);
            __m256 vr = _mm256_add_ps(va, vb);
            _mm256_storeu_ps(&result[i], vr);
        }
        
        for (size_t i = simd_size; i < size; ++i) {
            result[i] = a[i] + b[i];
        }
#else
        for (size_t i = 0; i < size; ++i) {
            result[i] = a[i] + b[i];
        }
#endif
    }
    
    // 高度优化的向量乘法
    static void mul_f32_optimized(const float* __restrict a, const float* __restrict b, 
                                 float* __restrict result, size_t size) {
#if defined(__AVX2__)
        const size_t simd_size = size & ~7ULL;
        
        // 循环展开 - 每次处理16个元素
        const size_t unroll_size = size & ~15ULL;
        for (size_t i = 0; i < unroll_size; i += 16) {
            __m256 va1 = _mm256_loadu_ps(&a[i]);
            __m256 vb1 = _mm256_loadu_ps(&b[i]);
            __m256 va2 = _mm256_loadu_ps(&a[i + 8]);
            __m256 vb2 = _mm256_loadu_ps(&b[i + 8]);
            
            __m256 vr1 = _mm256_mul_ps(va1, vb1);
            __m256 vr2 = _mm256_mul_ps(va2, vb2);
            
            _mm256_storeu_ps(&result[i], vr1);
            _mm256_storeu_ps(&result[i + 8], vr2);
        }
        
        // 处理剩余的8个元素
        for (size_t i = unroll_size; i < simd_size; i += 8) {
            __m256 va = _mm256_loadu_ps(&a[i]);
            __m256 vb = _mm256_loadu_ps(&b[i]);
            __m256 vr = _mm256_mul_ps(va, vb);
            _mm256_storeu_ps(&result[i], vr);
        }
        
        // 处理剩余元素
        for (size_t i = simd_size; i < size; ++i) {
            result[i] = a[i] * b[i];
        }
#else
        for (size_t i = 0; i < size; ++i) {
            result[i] = a[i] * b[i];
        }
#endif
    }
    
    // 高度优化的点积
    static float dot_f32_optimized(const float* __restrict a, const float* __restrict b, size_t size) {
#if defined(__AVX2__)
        __m256 sum = _mm256_setzero_ps();
        const size_t simd_size = size & ~7ULL;
        
        // 向量化累加
        for (size_t i = 0; i < simd_size; i += 8) {
            __m256 va = _mm256_loadu_ps(&a[i]);
            __m256 vb = _mm256_loadu_ps(&b[i]);
            sum = _mm256_fmadd_ps(va, vb, sum); // 使用FMA指令
        }
        
        // 水平求和
        __m128 sum_high = _mm256_extractf128_ps(sum, 1);
        __m128 sum_low = _mm256_castps256_ps128(sum);
        __m128 sum128 = _mm_add_ps(sum_high, sum_low);
        
        sum128 = _mm_hadd_ps(sum128, sum128);
        sum128 = _mm_hadd_ps(sum128, sum128);
        
        float result = _mm_cvtss_f32(sum128);
        
        // 处理剩余元素
        for (size_t i = simd_size; i < size; ++i) {
            result += a[i] * b[i];
        }
        
        return result;
#else
        float result = 0.0f;
        for (size_t i = 0; i < size; ++i) {
            result += a[i] * b[i];
        }
        return result;
#endif
    }
    
    // 内存对齐分配器
    static float* allocate_aligned(size_t count, size_t alignment = 32) {
        size_t bytes = count * sizeof(float);
        void* ptr = nullptr;
        
#ifdef _MSC_VER
        ptr = _aligned_malloc(bytes, alignment);
#else
        if (posix_memalign(&ptr, alignment, bytes) != 0) {
            ptr = nullptr;
        }
#endif
        
        return static_cast<float*>(ptr);
    }
    
    static void deallocate_aligned(float* ptr) {
        if (ptr) {
#ifdef _MSC_VER
            _aligned_free(ptr);
#else
            free(ptr);
#endif
        }
    }
    
    // 获取SIMD信息
    static std::string get_simd_info() {
#if defined(__AVX2__)
        return "AVX2 Optimized (High Performance)";
#elif defined(__AVX__)
        return "AVX Optimized";
#elif defined(__SSE4_2__)
        return "SSE4.2 Optimized";
#else
        return "Scalar (No SIMD)";
#endif
    }
};

/**
 * 高度优化的矩阵操作类
 */
class OptimizedMatrixOps {
public:
    // 分块矩阵乘法 - 缓存友好
    static void matmul_f32_blocked(const float* __restrict a, const float* __restrict b, 
                                  float* __restrict c, size_t m, size_t n, size_t k) {
        const size_t block_size = 64; // 缓存友好的分块大小
        
        // 初始化结果矩阵
        std::memset(c, 0, m * n * sizeof(float));
        
        // 分块矩阵乘法
        for (size_t bi = 0; bi < m; bi += block_size) {
            for (size_t bj = 0; bj < n; bj += block_size) {
                for (size_t bk = 0; bk < k; bk += block_size) {
                    
                    size_t i_end = std::min(bi + block_size, m);
                    size_t j_end = std::min(bj + block_size, n);
                    size_t k_end = std::min(bk + block_size, k);
                    
                    // 块内矩阵乘法
                    for (size_t i = bi; i < i_end; ++i) {
                        for (size_t j = bj; j < j_end; ++j) {
                            
#if defined(__AVX2__)
                            __m256 sum = _mm256_setzero_ps();
                            size_t kv = bk;
                            
                            // SIMD向量化内积 - 修复版本
                            for (; kv + 7 < k_end; kv += 8) {
                                __m256 va = _mm256_loadu_ps(&a[i * k + kv]);
                                __m256 vb = _mm256_loadu_ps(&b[kv * n + j]);
                                sum = _mm256_fmadd_ps(va, vb, sum);
                            }
                            
                            // 水平求和
                            float sum_array[8];
                            _mm256_storeu_ps(sum_array, sum);
                            float partial_sum = 0.0f;
                            for (int idx = 0; idx < 8; ++idx) {
                                partial_sum += sum_array[idx];
                            }
                            c[i * n + j] += partial_sum;
                            
                            // 处理剩余元素
                            for (size_t l = kv; l < k_end; ++l) {
                                c[i * n + j] += a[i * k + l] * b[l * n + j];
                            }
#else
                            for (size_t l = bk; l < k_end; ++l) {
                                c[i * n + j] += a[i * k + l] * b[l * n + j];
                            }
#endif
                        }
                    }
                }
            }
        }
    }
};

} // namespace simd
} // namespace phi
