#pragma once

// 简化的SIMD实现 - 专注于核心功能，避免编译器兼容性问题
#include <cstddef>
#include <cstring>
#include <algorithm>

// 只在支持的平台上启用SIMD
#if defined(__AVX2__) || defined(__AVX__) || (defined(_MSC_VER) && defined(__AVX2__))
    #define PHI_SIMD_ENABLED 1
    #ifdef _MSC_VER
        #include <intrin.h>
    #else
        #include <immintrin.h>
    #endif
#else
    #define PHI_SIMD_ENABLED 0
#endif

namespace phi {
namespace simd {

/**
 * 简化的SIMD操作 - 专注于核心功能
 */

// 内存对齐工具
inline void* aligned_malloc(size_t size, size_t alignment) {
#ifdef _MSC_VER
    return _aligned_malloc(size, alignment);
#else
    void* ptr = nullptr;
    if (posix_memalign(&ptr, alignment, size) != 0) {
        return nullptr;
    }
    return ptr;
#endif
}

inline void aligned_free(void* ptr) {
#ifdef _MSC_VER
    _aligned_free(ptr);
#else
    free(ptr);
#endif
}

// 检查内存对齐
inline bool is_aligned(const void* ptr, size_t alignment) {
    return (reinterpret_cast<uintptr_t>(ptr) % alignment) == 0;
}
    
    // 获取最佳指令集
    enum class InstructionSet {
        SCALAR,
        SSE,
        SSE2,
        SSE3,
        SSSE3,
        SSE41,
        SSE42,
        AVX,
        AVX2,
        AVX512F,
        AVX512DQ,
        AVX512BW
    };
    
    InstructionSet get_best_instruction_set() const;
    std::string get_instruction_set_name(InstructionSet set) const;
    void print_capabilities() const;

private:
    SimdCapabilities();
    void detect_capabilities();
    
    bool has_sse_ = false;
    bool has_sse2_ = false;
    bool has_sse3_ = false;
    bool has_ssse3_ = false;
    bool has_sse41_ = false;
    bool has_sse42_ = false;
    bool has_avx_ = false;
    bool has_avx2_ = false;
    bool has_avx512f_ = false;
    bool has_avx512dq_ = false;
    bool has_avx512bw_ = false;
    bool has_fma_ = false;
};

/**
 * SIMD优化的张量操作
 */
class SimdTensorOps {
public:
    SimdTensorOps();
    ~SimdTensorOps() = default;
    
    // 基础算术操作
    bool add(const Tensor& a, const Tensor& b, Tensor& result);
    bool subtract(const Tensor& a, const Tensor& b, Tensor& result);
    bool multiply(const Tensor& a, const Tensor& b, Tensor& result);
    bool divide(const Tensor& a, const Tensor& b, Tensor& result);
    
    // 标量操作
    bool add_scalar(const Tensor& input, float scalar, Tensor& result);
    bool multiply_scalar(const Tensor& input, float scalar, Tensor& result);
    
    // 矩阵操作
    bool matmul(const Tensor& a, const Tensor& b, Tensor& result, 
                bool transpose_a = false, bool transpose_b = false);
    bool gemv(const Tensor& matrix, const Tensor& vector, Tensor& result, 
              bool transpose = false, float alpha = 1.0f, float beta = 0.0f);
    
    // 激活函数
    bool relu(const Tensor& input, Tensor& output);
    bool leaky_relu(const Tensor& input, Tensor& output, float negative_slope = 0.01f);
    bool sigmoid(const Tensor& input, Tensor& output);
    bool tanh(const Tensor& input, Tensor& output);
    bool gelu(const Tensor& input, Tensor& output);
    bool swish(const Tensor& input, Tensor& output);
    
    // 归一化操作
    bool layer_norm(const Tensor& input, const Tensor& weight, const Tensor& bias,
                    Tensor& output, float eps = 1e-5f);
    bool batch_norm(const Tensor& input, const Tensor& weight, const Tensor& bias,
                    const Tensor& running_mean, const Tensor& running_var,
                    Tensor& output, bool training = true, float momentum = 0.1f, float eps = 1e-5f);
    
    // 池化操作
    bool max_pool2d(const Tensor& input, Tensor& output, 
                    int kernel_h, int kernel_w, int stride_h = 1, int stride_w = 1,
                    int pad_h = 0, int pad_w = 0);
    bool avg_pool2d(const Tensor& input, Tensor& output,
                    int kernel_h, int kernel_w, int stride_h = 1, int stride_w = 1,
                    int pad_h = 0, int pad_w = 0);
    
    // 卷积操作
    bool conv2d(const Tensor& input, const Tensor& weight, const Tensor& bias,
                Tensor& output, int stride_h = 1, int stride_w = 1,
                int pad_h = 0, int pad_w = 0, int dilation_h = 1, int dilation_w = 1);
    
    // 统计操作
    bool sum(const Tensor& input, Tensor& output, const std::vector<int>& axes = {},
             bool keepdims = false);
    bool mean(const Tensor& input, Tensor& output, const std::vector<int>& axes = {},
              bool keepdims = false);
    bool variance(const Tensor& input, Tensor& output, const std::vector<int>& axes = {},
                  bool keepdims = false, bool unbiased = true);
    bool min(const Tensor& input, Tensor& output, const std::vector<int>& axes = {},
             bool keepdims = false);
    bool max(const Tensor& input, Tensor& output, const std::vector<int>& axes = {},
             bool keepdims = false);
    
    // 工具函数
    bool fill(Tensor& tensor, float value);
    bool copy(const Tensor& src, Tensor& dst);
    bool transpose(const Tensor& input, Tensor& output, const std::vector<int>& dims);
    bool reshape(const Tensor& input, Tensor& output, const std::vector<size_t>& new_shape);
    
    // 比较操作
    bool equal(const Tensor& a, const Tensor& b, Tensor& result);
    bool greater(const Tensor& a, const Tensor& b, Tensor& result);
    bool less(const Tensor& a, const Tensor& b, Tensor& result);
    
    // 逻辑操作
    bool logical_and(const Tensor& a, const Tensor& b, Tensor& result);
    bool logical_or(const Tensor& a, const Tensor& b, Tensor& result);
    bool logical_not(const Tensor& input, Tensor& result);
    
    // 数学函数
    bool exp(const Tensor& input, Tensor& output);
    bool log(const Tensor& input, Tensor& output);
    bool sqrt(const Tensor& input, Tensor& output);
    bool pow(const Tensor& input, float exponent, Tensor& output);
    bool sin(const Tensor& input, Tensor& output);
    bool cos(const Tensor& input, Tensor& output);
    
    // 性能分析
    struct PerformanceMetrics {
        double total_time_ms;
        double simd_time_ms;
        double scalar_fallback_time_ms;
        size_t operations_count;
        double throughput_gflops;
        SimdCapabilities::InstructionSet used_instruction_set;
    };
    
    PerformanceMetrics get_performance_metrics() const;
    void reset_performance_metrics();
    void enable_profiling(bool enable);

private:
    SimdCapabilities::InstructionSet best_instruction_set_;
    bool profiling_enabled_ = false;
    mutable PerformanceMetrics metrics_;
    
    // 内部实现函数 - AVX512
    bool add_avx512(const float* a, const float* b, float* result, size_t size);
    bool multiply_avx512(const float* a, const float* b, float* result, size_t size);
    bool relu_avx512(const float* input, float* output, size_t size);
    bool sigmoid_avx512(const float* input, float* output, size_t size);
    bool exp_avx512(const float* input, float* output, size_t size);
    
    // 内部实现函数 - AVX2
    bool add_avx2(const float* a, const float* b, float* result, size_t size);
    bool multiply_avx2(const float* a, const float* b, float* result, size_t size);
    bool relu_avx2(const float* input, float* output, size_t size);
    bool sigmoid_avx2(const float* input, float* output, size_t size);
    bool exp_avx2(const float* input, float* output, size_t size);
    
    // 内部实现函数 - AVX
    bool add_avx(const float* a, const float* b, float* result, size_t size);
    bool multiply_avx(const float* a, const float* b, float* result, size_t size);
    bool relu_avx(const float* input, float* output, size_t size);
    bool sigmoid_avx(const float* input, float* output, size_t size);
    bool exp_avx(const float* input, float* output, size_t size);
    
    // 内部实现函数 - SSE
    bool add_sse(const float* a, const float* b, float* result, size_t size);
    bool multiply_sse(const float* a, const float* b, float* result, size_t size);
    bool relu_sse(const float* input, float* output, size_t size);
    bool sigmoid_sse(const float* input, float* output, size_t size);
    bool exp_sse(const float* input, float* output, size_t size);
    
    // 标量回退实现
    bool add_scalar_impl(const float* a, const float* b, float* result, size_t size);
    bool multiply_scalar_impl(const float* a, const float* b, float* result, size_t size);
    bool relu_scalar_impl(const float* input, float* output, size_t size);
    bool sigmoid_scalar_impl(const float* input, float* output, size_t size);
    bool exp_scalar_impl(const float* input, float* output, size_t size);
    
    // 矩阵乘法优化实现
    bool gemm_avx512(const float* a, const float* b, float* c,
                     int m, int n, int k, bool transpose_a, bool transpose_b,
                     float alpha = 1.0f, float beta = 0.0f);
    bool gemm_avx2(const float* a, const float* b, float* c,
                   int m, int n, int k, bool transpose_a, bool transpose_b,
                   float alpha = 1.0f, float beta = 0.0f);
    bool gemm_avx(const float* a, const float* b, float* c,
                  int m, int n, int k, bool transpose_a, bool transpose_b,
                  float alpha = 1.0f, float beta = 0.0f);
    
    // 卷积优化实现
    bool conv2d_im2col_avx2(const float* input, const float* weight, const float* bias,
                            float* output, int batch_size, int in_channels, int out_channels,
                            int input_h, int input_w, int kernel_h, int kernel_w,
                            int stride_h, int stride_w, int pad_h, int pad_w);
    
    // 工具函数
    bool check_tensor_compatibility(const Tensor& a, const Tensor& b) const;
    bool allocate_output_tensor(Tensor& output, const std::vector<size_t>& shape, DataType dtype) const;
    size_t get_aligned_size(size_t size, size_t alignment = 32) const;
    bool is_aligned(const void* ptr, size_t alignment = 32) const;
    
    // 性能测量
    void start_timing() const;
    void end_timing(SimdCapabilities::InstructionSet used_set) const;
};

/**
 * SIMD内存对齐分配器
 */
class AlignedAllocator {
public:
    static void* allocate(size_t size, size_t alignment = 64);
    static void deallocate(void* ptr);
    static bool is_aligned(const void* ptr, size_t alignment = 64);
    
    // RAII包装器
    template<typename T>
    class AlignedPtr {
    public:
        AlignedPtr(size_t count, size_t alignment = 64)
            : ptr_(static_cast<T*>(AlignedAllocator::allocate(count * sizeof(T), alignment)))
            , size_(count) {}
        
        ~AlignedPtr() {
            if (ptr_) AlignedAllocator::deallocate(ptr_);
        }
        
        // 禁止拷贝
        AlignedPtr(const AlignedPtr&) = delete;
        AlignedPtr& operator=(const AlignedPtr&) = delete;
        
        // 允许移动
        AlignedPtr(AlignedPtr&& other) noexcept
            : ptr_(other.ptr_), size_(other.size_) {
            other.ptr_ = nullptr;
            other.size_ = 0;
        }
        
        AlignedPtr& operator=(AlignedPtr&& other) noexcept {
            if (this != &other) {
                if (ptr_) AlignedAllocator::deallocate(ptr_);
                ptr_ = other.ptr_;
                size_ = other.size_;
                other.ptr_ = nullptr;
                other.size_ = 0;
            }
            return *this;
        }
        
        T* get() const { return ptr_; }
        T& operator[](size_t index) { return ptr_[index]; }
        const T& operator[](size_t index) const { return ptr_[index]; }
        size_t size() const { return size_; }
        
        explicit operator bool() const { return ptr_ != nullptr; }
        
    private:
        T* ptr_;
        size_t size_;
    };
};

/**
 * SIMD优化的数学函数库
 */
namespace math {
    // 快速数学函数
    void fast_exp_avx2(const float* input, float* output, size_t size);
    void fast_log_avx2(const float* input, float* output, size_t size);
    void fast_tanh_avx2(const float* input, float* output, size_t size);
    void fast_sigmoid_avx2(const float* input, float* output, size_t size);
    void fast_gelu_avx2(const float* input, float* output, size_t size);
    
    // 三角函数
    void sin_avx2(const float* input, float* output, size_t size);
    void cos_avx2(const float* input, float* output, size_t size);
    
    // 统计函数
    float sum_avx2(const float* input, size_t size);
    float mean_avx2(const float* input, size_t size);
    float variance_avx2(const float* input, size_t size, float mean);
    float min_avx2(const float* input, size_t size);
    float max_avx2(const float* input, size_t size);
    
    // 向量操作
    float dot_product_avx2(const float* a, const float* b, size_t size);
    void vector_norm_avx2(const float* input, float* output, size_t size);
    void vector_normalize_avx2(const float* input, float* output, size_t size);
}

} // namespace simd
} // namespace phi
