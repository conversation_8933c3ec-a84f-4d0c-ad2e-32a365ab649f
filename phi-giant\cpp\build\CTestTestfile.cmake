# CMake generated Testfile for 
# Source directory: E:/an/phi-giant/cpp
# Build directory: E:/an/phi-giant/cpp/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(phi_core_test "E:/an/phi-giant/cpp/build/Debug/test_phi.exe")
  set_tests_properties(phi_core_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;347;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(phi_core_test "E:/an/phi-giant/cpp/build/Release/test_phi.exe")
  set_tests_properties(phi_core_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;347;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(phi_core_test "E:/an/phi-giant/cpp/build/MinSizeRel/test_phi.exe")
  set_tests_properties(phi_core_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;347;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(phi_core_test "E:/an/phi-giant/cpp/build/RelWithDebInfo/test_phi.exe")
  set_tests_properties(phi_core_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;347;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
else()
  add_test(phi_core_test NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(phi_memory_safety_test "E:/an/phi-giant/cpp/build/Debug/test_memory_safety_final.exe")
  set_tests_properties(phi_memory_safety_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;348;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(phi_memory_safety_test "E:/an/phi-giant/cpp/build/Release/test_memory_safety_final.exe")
  set_tests_properties(phi_memory_safety_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;348;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(phi_memory_safety_test "E:/an/phi-giant/cpp/build/MinSizeRel/test_memory_safety_final.exe")
  set_tests_properties(phi_memory_safety_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;348;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(phi_memory_safety_test "E:/an/phi-giant/cpp/build/RelWithDebInfo/test_memory_safety_final.exe")
  set_tests_properties(phi_memory_safety_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;348;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
else()
  add_test(phi_memory_safety_test NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(phi_enterprise_final_test "E:/an/phi-giant/cpp/build/Debug/test_enterprise_final.exe")
  set_tests_properties(phi_enterprise_final_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;349;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(phi_enterprise_final_test "E:/an/phi-giant/cpp/build/Release/test_enterprise_final.exe")
  set_tests_properties(phi_enterprise_final_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;349;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(phi_enterprise_final_test "E:/an/phi-giant/cpp/build/MinSizeRel/test_enterprise_final.exe")
  set_tests_properties(phi_enterprise_final_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;349;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(phi_enterprise_final_test "E:/an/phi-giant/cpp/build/RelWithDebInfo/test_enterprise_final.exe")
  set_tests_properties(phi_enterprise_final_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;349;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
else()
  add_test(phi_enterprise_final_test NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(phi_enterprise_week1_test "E:/an/phi-giant/cpp/build/Debug/test_enterprise_week1.exe")
  set_tests_properties(phi_enterprise_week1_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;350;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(phi_enterprise_week1_test "E:/an/phi-giant/cpp/build/Release/test_enterprise_week1.exe")
  set_tests_properties(phi_enterprise_week1_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;350;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(phi_enterprise_week1_test "E:/an/phi-giant/cpp/build/MinSizeRel/test_enterprise_week1.exe")
  set_tests_properties(phi_enterprise_week1_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;350;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(phi_enterprise_week1_test "E:/an/phi-giant/cpp/build/RelWithDebInfo/test_enterprise_week1.exe")
  set_tests_properties(phi_enterprise_week1_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;350;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
else()
  add_test(phi_enterprise_week1_test NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(phi_simd_complete_test "E:/an/phi-giant/cpp/build/Debug/test_simd_complete.exe")
  set_tests_properties(phi_simd_complete_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;351;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(phi_simd_complete_test "E:/an/phi-giant/cpp/build/Release/test_simd_complete.exe")
  set_tests_properties(phi_simd_complete_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;351;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(phi_simd_complete_test "E:/an/phi-giant/cpp/build/MinSizeRel/test_simd_complete.exe")
  set_tests_properties(phi_simd_complete_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;351;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(phi_simd_complete_test "E:/an/phi-giant/cpp/build/RelWithDebInfo/test_simd_complete.exe")
  set_tests_properties(phi_simd_complete_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;351;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
else()
  add_test(phi_simd_complete_test NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(phi_memory_optimization_test "E:/an/phi-giant/cpp/build/Debug/test_memory_optimization.exe")
  set_tests_properties(phi_memory_optimization_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;352;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(phi_memory_optimization_test "E:/an/phi-giant/cpp/build/Release/test_memory_optimization.exe")
  set_tests_properties(phi_memory_optimization_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;352;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(phi_memory_optimization_test "E:/an/phi-giant/cpp/build/MinSizeRel/test_memory_optimization.exe")
  set_tests_properties(phi_memory_optimization_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;352;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(phi_memory_optimization_test "E:/an/phi-giant/cpp/build/RelWithDebInfo/test_memory_optimization.exe")
  set_tests_properties(phi_memory_optimization_test PROPERTIES  _BACKTRACE_TRIPLES "E:/an/phi-giant/cpp/CMakeLists.txt;352;add_test;E:/an/phi-giant/cpp/CMakeLists.txt;0;")
else()
  add_test(phi_memory_optimization_test NOT_AVAILABLE)
endif()
