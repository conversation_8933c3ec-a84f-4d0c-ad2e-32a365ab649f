@echo off
REM Test simple CUDA compilation
echo Setting up Visual Studio environment...
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

echo Compiling simple CUDA test...
nvcc -O3 --use_fast_math -std=c++17 -arch=sm_75 -c cuda_test_simple.cu -o cuda_test_simple.obj

if %ERRORLEVEL% EQU 0 (
    echo Simple CUDA compilation successful!
    echo Output: cuda_test_simple.obj
) else (
    echo Simple CUDA compilation failed!
    exit /b 1
)

echo Simple CUDA test completed!
