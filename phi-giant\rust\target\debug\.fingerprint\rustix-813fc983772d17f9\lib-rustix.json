{"rustc": 1842507548689473721, "features": "[\"alloc\", \"fs\", \"libc-extra-traits\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 1442335954752063332, "path": 14610429897480688674, "deps": [[3430646239657634944, "build_script_build", false, 14037020237414493719], [7896293946984509699, "bitflags", false, 10835267859002908756], [8253628577145923712, "libc_errno", false, 9667518650520919143], [10281541584571964250, "windows_sys", false, 4778959745565641195]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustix-813fc983772d17f9\\dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}