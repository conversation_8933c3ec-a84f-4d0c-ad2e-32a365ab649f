use std::env;
use std::path::PathBuf;

fn main() {
    println!("cargo:rerun-if-changed=../cpp");
    
    // Get the output directory
    let out_dir = env::var("OUT_DIR").unwrap();
    let manifest_dir = env::var("CARGO_MANIFEST_DIR").unwrap();
    
    // Path to the C++ library
    let cpp_build_dir = PathBuf::from(&manifest_dir)
        .parent()
        .unwrap()
        .join("cpp")
        .join("build")
        .join("Release");
    
    // Tell cargo where to find the C++ library
    println!("cargo:rustc-link-search=native={}", cpp_build_dir.display());
    
    // Link the C++ library
    println!("cargo:rustc-link-lib=static=phi_core");
    
    // Link system libraries that the C++ library depends on
    #[cfg(target_os = "windows")]
    {
        println!("cargo:rustc-link-lib=dylib=user32");
        println!("cargo:rustc-link-lib=dylib=kernel32");
        println!("cargo:rustc-link-lib=dylib=advapi32");
        println!("cargo:rustc-link-lib=dylib=ws2_32");
    }
    
    #[cfg(target_os = "linux")]
    {
        println!("cargo:rustc-link-lib=dylib=pthread");
        println!("cargo:rustc-link-lib=dylib=dl");
        println!("cargo:rustc-link-lib=dylib=m");
    }
    
    #[cfg(target_os = "macos")]
    {
        println!("cargo:rustc-link-lib=dylib=pthread");
        println!("cargo:rustc-link-lib=dylib=dl");
        println!("cargo:rustc-link-lib=dylib=m");
        println!("cargo:rustc-link-lib=framework=Foundation");
        println!("cargo:rustc-link-lib=framework=Security");
    }
    
    // Skip automatic binding generation for now
    // Manual bindings are provided in src/ffi.rs
}
