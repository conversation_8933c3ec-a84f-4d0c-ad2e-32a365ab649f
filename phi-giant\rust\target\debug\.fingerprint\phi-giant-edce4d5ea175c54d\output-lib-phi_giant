{"$message_type":"diagnostic","message":"returned pointer of `as_ptr` call is never null, so checking it for null will always return false","code":{"code":"useless_ptr_null_checks","explanation":null},"level":"warning","spans":[{"file_name":"src\\ffi_zero_copy.rs","byte_start":14443,"byte_end":14471,"line_start":460,"line_end":460,"column_start":10,"column_end":38,"is_primary":true,"text":[{"text":"        !self.data.as_ptr().is_null() &&","highlight_start":10,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(useless_ptr_null_checks)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: returned pointer of `as_ptr` call is never null, so checking it for null will always return false\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ffi_zero_copy.rs:460:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m460\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        !self.data.as_ptr().is_null() &&\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(useless_ptr_null_checks)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"returned pointer of `as_ptr` call is never null, so checking it for null will always return false","code":{"code":"useless_ptr_null_checks","explanation":null},"level":"warning","spans":[{"file_name":"src\\ffi_zero_copy.rs","byte_start":14484,"byte_end":14513,"line_start":461,"line_end":461,"column_start":10,"column_end":39,"is_primary":true,"text":[{"text":"        !self.shape.as_ptr().is_null() &&","highlight_start":10,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: returned pointer of `as_ptr` call is never null, so checking it for null will always return false\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ffi_zero_copy.rs:461:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        !self.shape.as_ptr().is_null() &&\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"returned pointer of `as_ptr` call is never null, so checking it for null will always return false","code":{"code":"useless_ptr_null_checks","explanation":null},"level":"warning","spans":[{"file_name":"src\\ffi_zero_copy.rs","byte_start":14526,"byte_end":14557,"line_start":462,"line_end":462,"column_start":10,"column_end":41,"is_primary":true,"text":[{"text":"        !self.strides.as_ptr().is_null() &&","highlight_start":10,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: returned pointer of `as_ptr` call is never null, so checking it for null will always return false\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ffi_zero_copy.rs:462:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m462\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        !self.strides.as_ptr().is_null() &&\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`phi_tensor_copy` redeclared with a different signature","code":{"code":"clashing_extern_declarations","explanation":null},"level":"warning","spans":[{"file_name":"src\\ffi.rs","byte_start":2240,"byte_end":2309,"line_start":96,"line_end":96,"column_start":5,"column_end":74,"is_primary":false,"text":[{"text":"    pub fn phi_tensor_copy(src: *const CPhiTensor, dst: *mut CPhiTensor);","highlight_start":5,"highlight_end":74}],"label":"`phi_tensor_copy` previously declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ffi_unified.rs","byte_start":3672,"byte_end":3815,"line_start":154,"line_end":158,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn phi_tensor_copy(","highlight_start":5,"highlight_end":28},{"text":"        src: *const PhiTensorDesc,","highlight_start":1,"highlight_end":35},{"text":"        dst: *mut PhiTensorDesc,","highlight_start":1,"highlight_end":33},{"text":"        mode: PhiComputeMode","highlight_start":1,"highlight_end":29},{"text":"    ) -> PhiErrorCode;","highlight_start":1,"highlight_end":23}],"label":"this signature doesn't match the previous declaration","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `unsafe extern \"C\" fn(*const CPhiTensor, *mut CPhiTensor)`\n   found `unsafe extern \"C\" fn(*const PhiTensorDesc, *mut PhiTensorDesc, ffi_unified::PhiComputeMode) -> ffi_unified::PhiErrorCode`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clashing_extern_declarations)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `phi_tensor_copy` redeclared with a different signature\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ffi_unified.rs:154:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn phi_tensor_copy(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        src: *const PhiTensorDesc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m156\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        dst: *mut PhiTensorDesc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m157\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mode: PhiComputeMode\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> PhiErrorCode;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|______________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthis signature doesn't match the previous declaration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\ffi.rs:96:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    pub fn phi_tensor_copy(src: *const CPhiTensor, dst: *mut CPhiTensor);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`phi_tensor_copy` previously declared here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected `unsafe extern \"C\" fn(*const CPhiTensor, *mut CPhiTensor)`\u001b[0m\n\u001b[0m               found `\u001b[0m\u001b[0m\u001b[1m\u001b[35munsafe extern \"C\" fn(*const PhiTensorDesc, *mut PhiTensorDesc, ffi_unified::PhiComputeMode) -> ffi_unified::PhiErrorCode\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clashing_extern_declarations)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`phi_cuda_set_device` redeclared with a different signature","code":{"code":"clashing_extern_declarations","explanation":null},"level":"warning","spans":[{"file_name":"src\\ffi.rs","byte_start":7974,"byte_end":8016,"line_start":310,"line_end":310,"column_start":5,"column_end":47,"is_primary":false,"text":[{"text":"    pub fn phi_cuda_set_device(device: c_int);","highlight_start":5,"highlight_end":47}],"label":"`phi_cuda_set_device` previously declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ffi_unified.rs","byte_start":5897,"byte_end":5958,"line_start":235,"line_end":235,"column_start":5,"column_end":66,"is_primary":true,"text":[{"text":"    pub fn phi_cuda_set_device(device_id: c_int) -> PhiErrorCode;","highlight_start":5,"highlight_end":66}],"label":"this signature doesn't match the previous declaration","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `unsafe extern \"C\" fn(i32)`\n   found `unsafe extern \"C\" fn(i32) -> ffi_unified::PhiErrorCode`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `phi_cuda_set_device` redeclared with a different signature\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ffi_unified.rs:235:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m235\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn phi_cuda_set_device(device_id: c_int) -> PhiErrorCode;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthis signature doesn't match the previous declaration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\ffi.rs:310:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn phi_cuda_set_device(device: c_int);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`phi_cuda_set_device` previously declared here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected `unsafe extern \"C\" fn(i32)`\u001b[0m\n\u001b[0m               found `\u001b[0m\u001b[0m\u001b[1m\u001b[35munsafe extern \"C\" fn(i32) -> ffi_unified::PhiErrorCode\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`phi_cuda_stream_synchronize` redeclared with a different signature","code":{"code":"clashing_extern_declarations","explanation":null},"level":"warning","spans":[{"file_name":"src\\ffi.rs","byte_start":7533,"byte_end":7589,"line_start":293,"line_end":293,"column_start":5,"column_end":61,"is_primary":false,"text":[{"text":"    pub fn phi_cuda_stream_synchronize(stream: *mut c_void);","highlight_start":5,"highlight_end":61}],"label":"`phi_cuda_stream_synchronize` previously declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ffi_unified.rs","byte_start":6200,"byte_end":6269,"line_start":242,"line_end":242,"column_start":5,"column_end":74,"is_primary":true,"text":[{"text":"    pub fn phi_cuda_stream_synchronize(stream_id: c_int) -> PhiErrorCode;","highlight_start":5,"highlight_end":74}],"label":"this signature doesn't match the previous declaration","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `unsafe extern \"C\" fn(*mut c_void)`\n   found `unsafe extern \"C\" fn(i32) -> ffi_unified::PhiErrorCode`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `phi_cuda_stream_synchronize` redeclared with a different signature\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ffi_unified.rs:242:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn phi_cuda_stream_synchronize(stream_id: c_int) -> PhiErrorCode;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthis signature doesn't match the previous declaration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\ffi.rs:293:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn phi_cuda_stream_synchronize(stream: *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`phi_cuda_stream_synchronize` previously declared here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected `unsafe extern \"C\" fn(*mut c_void)`\u001b[0m\n\u001b[0m               found `\u001b[0m\u001b[0m\u001b[1m\u001b[35munsafe extern \"C\" fn(i32) -> ffi_unified::PhiErrorCode\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`phi_clear_error` redeclared with a different signature","code":{"code":"clashing_extern_declarations","explanation":null},"level":"warning","spans":[{"file_name":"src\\ffi.rs","byte_start":7755,"byte_end":7780,"line_start":301,"line_end":301,"column_start":5,"column_end":30,"is_primary":false,"text":[{"text":"    pub fn phi_clear_error();","highlight_start":5,"highlight_end":30}],"label":"`phi_clear_error` previously declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ffi_unified.rs","byte_start":8513,"byte_end":8554,"line_start":322,"line_end":322,"column_start":5,"column_end":46,"is_primary":true,"text":[{"text":"    pub fn phi_clear_error() -> PhiErrorCode;","highlight_start":5,"highlight_end":46}],"label":"this signature doesn't match the previous declaration","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `unsafe extern \"C\" fn()`\n   found `unsafe extern \"C\" fn() -> ffi_unified::PhiErrorCode`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `phi_clear_error` redeclared with a different signature\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ffi_unified.rs:322:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn phi_clear_error() -> PhiErrorCode;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthis signature doesn't match the previous declaration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\ffi.rs:301:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn phi_clear_error();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`phi_clear_error` previously declared here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected `unsafe extern \"C\" fn()`\u001b[0m\n\u001b[0m               found `\u001b[0m\u001b[0m\u001b[1m\u001b[35munsafe extern \"C\" fn() -> ffi_unified::PhiErrorCode\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 7 warnings emitted\u001b[0m\n\n"}
