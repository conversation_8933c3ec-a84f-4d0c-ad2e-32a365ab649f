//! Phi-Giant - Minimal version for FFI testing
//! 
//! This is a minimal version of the library that only includes
//! the new FFI bindings for testing purposes.

#![allow(dead_code)]
#![allow(unused_imports)]

pub mod error;
pub mod config;
pub mod ffi_new;

use anyhow::Result;

/// Framework version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = "Phi-Giant";
pub const DESCRIPTION: &str = "Philosophy Neural Network Framework";

/// Initialize the framework
pub fn init() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();
    
    // Log framework information
    tracing::info!("Initializing {} v{}", NAME, VERSION);
    tracing::info!("Description: {}", DESCRIPTION);
    
    // Check CUDA availability
    let cuda_available = ffi_new::cuda_is_available();
    let cuda_devices = ffi_new::cuda_device_count();
    
    tracing::info!("CUDA available: {}", cuda_available);
    tracing::info!("CUDA devices: {}", cuda_devices);
    
    Ok(())
}

/// Get framework information
pub fn info() -> FrameworkInfo {
    FrameworkInfo {
        name: NAME.to_string(),
        version: VERSION.to_string(),
        description: DESCRIPTION.to_string(),
        cuda_available: ffi_new::cuda_is_available(),
        cuda_device_count: ffi_new::cuda_device_count(),
    }
}

/// Framework information structure
#[derive(Debug, Clone)]
pub struct FrameworkInfo {
    pub name: String,
    pub version: String,
    pub description: String,
    pub cuda_available: bool,
    pub cuda_device_count: i32,
}

/// Simple tensor wrapper for high-level API
pub struct Tensor {
    inner: ffi_new::SafeTensor,
}

impl Tensor {
    /// Create a new tensor from data and shape
    pub fn new(data: &[f32], shape: &[i64]) -> Result<Self> {
        let inner = ffi_new::SafeTensor::new(data, shape)
            .map_err(|e| anyhow::anyhow!("Failed to create tensor: {}", e))?;
        Ok(Tensor { inner })
    }
    
    /// Create a tensor of zeros
    pub fn zeros(shape: &[i64]) -> Result<Self> {
        let inner = ffi_new::SafeTensor::zeros(shape)
            .map_err(|e| anyhow::anyhow!("Failed to create zeros tensor: {}", e))?;
        Ok(Tensor { inner })
    }
    
    /// Create a tensor of ones
    pub fn ones(shape: &[i64]) -> Result<Self> {
        let inner = ffi_new::SafeTensor::ones(shape)
            .map_err(|e| anyhow::anyhow!("Failed to create ones tensor: {}", e))?;
        Ok(Tensor { inner })
    }
    
    /// Get tensor shape
    pub fn shape(&self) -> Vec<i64> {
        self.inner.shape()
    }
    
    /// Get tensor size (total number of elements)
    pub fn size(&self) -> usize {
        self.inner.size()
    }
    
    /// Get tensor dimensions
    pub fn ndim(&self) -> usize {
        self.inner.ndim()
    }
    
    /// Get tensor data as slice
    pub fn data(&self) -> &[f32] {
        self.inner.data()
    }
    
    /// Apply ReLU activation
    pub fn relu(&self) -> Result<Tensor> {
        let inner = self.inner.relu()
            .map_err(|e| anyhow::anyhow!("Failed to apply ReLU: {}", e))?;
        Ok(Tensor { inner })
    }
    
    /// Apply sigmoid activation
    pub fn sigmoid(&self) -> Result<Tensor> {
        let inner = self.inner.sigmoid()
            .map_err(|e| anyhow::anyhow!("Failed to apply sigmoid: {}", e))?;
        Ok(Tensor { inner })
    }
    
    /// Add two tensors
    pub fn add(&self, other: &Tensor) -> Result<Tensor> {
        let inner = self.inner.add(&other.inner)
            .map_err(|e| anyhow::anyhow!("Failed to add tensors: {}", e))?;
        Ok(Tensor { inner })
    }
    
    /// Multiply two tensors element-wise
    pub fn mul(&self, other: &Tensor) -> Result<Tensor> {
        let inner = self.inner.mul(&other.inner)
            .map_err(|e| anyhow::anyhow!("Failed to multiply tensors: {}", e))?;
        Ok(Tensor { inner })
    }
}

/// SIMD utilities
pub mod simd {
    use super::ffi_new;
    use anyhow::Result;
    
    /// Compute dot product using SIMD
    pub fn dot_product(a: &[f32], b: &[f32]) -> Result<f32> {
        ffi_new::simd_dot_product(a, b)
            .map_err(|e| anyhow::anyhow!("SIMD dot product failed: {}", e))
    }
    
    /// Add vectors using SIMD
    pub fn add_vectors(a: &[f32], b: &[f32]) -> Result<Vec<f32>> {
        ffi_new::simd_add_vectors(a, b)
            .map_err(|e| anyhow::anyhow!("SIMD vector addition failed: {}", e))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_framework_init() {
        let result = init();
        assert!(result.is_ok());
    }
    
    #[test]
    fn test_framework_info() {
        let info = info();
        assert_eq!(info.name, "Phi-Giant");
        assert!(!info.version.is_empty());
    }
    
    #[test]
    fn test_tensor_operations() {
        let data = vec![1.0, 2.0, 3.0, 4.0];
        let shape = vec![2, 2];
        
        let tensor = Tensor::new(&data, &shape).unwrap();
        assert_eq!(tensor.shape(), vec![2, 2]);
        assert_eq!(tensor.size(), 4);
        assert_eq!(tensor.data(), &[1.0, 2.0, 3.0, 4.0]);
        
        let relu_result = tensor.relu().unwrap();
        assert_eq!(relu_result.data(), &[1.0, 2.0, 3.0, 4.0]);
    }
    
    #[test]
    fn test_simd_operations() {
        let a = vec![1.0, 2.0, 3.0, 4.0];
        let b = vec![5.0, 6.0, 7.0, 8.0];
        
        let dot = simd::dot_product(&a, &b).unwrap();
        assert!((dot - 70.0).abs() < 1e-6);
        
        let sum = simd::add_vectors(&a, &b).unwrap();
        assert_eq!(sum, vec![6.0, 8.0, 10.0, 12.0]);
    }
}
