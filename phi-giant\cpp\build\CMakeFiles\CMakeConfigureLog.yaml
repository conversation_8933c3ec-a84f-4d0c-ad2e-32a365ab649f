
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLanguage.cmake:140 (message)"
      - "CMakeLists.txt:5 (check_language)"
    checks:
      - "Looking for a CUDA compiler"
    message: |
      Looking for a CUDA compiler failed with the following output:
      -- Selecting Windows SDK version 10.0.22000.0 to target Windows 10.0.26100.
      CMake Error at C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:614 (message):
        No CUDA toolset found.
      Call Stack (most recent call first):
        C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)
        C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:53 (__determine_compiler_id_test)
        C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCUDACompiler.cmake:131 (CMAKE_DETERMINE_COMPILER_ID)
        CMakeLists.txt:3 (project)
      
      
      -- Configuring incomplete, errors occurred!
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/8/1 18:53:50。
      节点 1 上的项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:00.43
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/an/phi-giant/cpp/build/CMakeFiles/3.31.7/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-eb9cv2"
      binary: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-eb9cv2"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-eb9cv2'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_aadab.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/1 18:53:51。
        节点 1 上的项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eb9cv2\\cmTC_aadab.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_aadab.dir\\Debug\\”。
          正在创建目录“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eb9cv2\\Debug\\”。
          正在创建目录“cmTC_aadab.dir\\Debug\\cmTC_aadab.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_aadab.dir\\Debug\\cmTC_aadab.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_aadab.dir\\Debug\\\\" /Fd"cmTC_aadab.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CMakeCXXCompilerABI.cpp
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_aadab.dir\\Debug\\\\" /Fd"cmTC_aadab.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eb9cv2\\Debug\\cmTC_aadab.exe" /INCREMENTAL /ILK:"cmTC_aadab.dir\\Debug\\cmTC_aadab.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-eb9cv2/Debug/cmTC_aadab.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-eb9cv2/Debug/cmTC_aadab.lib" /MACHINE:X64  /machine:x64 cmTC_aadab.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_aadab.vcxproj -> E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eb9cv2\\Debug\\cmTC_aadab.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_aadab.dir\\Debug\\cmTC_aadab.tlog\\unsuccessfulbuild”。
          正在对“cmTC_aadab.dir\\Debug\\cmTC_aadab.tlog\\cmTC_aadab.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eb9cv2\\cmTC_aadab.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:00.33
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:56 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-a8twlv"
      binary: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-a8twlv"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /fp:fast"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-a8twlv'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_a480a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/1 18:53:51。
        节点 1 上的项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\cmTC_a480a.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_a480a.dir\\Debug\\”。
          正在创建目录“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\Debug\\”。
          正在创建目录“cmTC_a480a.dir\\Debug\\cmTC_a480a.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_a480a.dir\\Debug\\cmTC_a480a.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_a480a.dir\\Debug\\\\" /Fd"cmTC_a480a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\src.cxx"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          src.cxx
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_a480a.dir\\Debug\\\\" /Fd"cmTC_a480a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\src.cxx"
        E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\src.cxx(1,10): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\cmTC_a480a.vcxproj]
        已完成生成项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\cmTC_a480a.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\cmTC_a480a.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\src.cxx(1,10): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a8twlv\\cmTC_a480a.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.24
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:56 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-gs5ecc"
      binary: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-gs5ecc"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /fp:fast"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-gs5ecc'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_c518d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/1 18:53:51。
        节点 1 上的项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\cmTC_c518d.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_c518d.dir\\Debug\\”。
          正在创建目录“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\Debug\\”。
          正在创建目录“cmTC_c518d.dir\\Debug\\cmTC_c518d.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_c518d.dir\\Debug\\cmTC_c518d.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c518d.dir\\Debug\\\\" /Fd"cmTC_c518d.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\CheckFunctionExists.cxx"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          CheckFunctionExists.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c518d.dir\\Debug\\\\" /Fd"cmTC_c518d.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\CheckFunctionExists.cxx"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\Debug\\cmTC_c518d.exe" /INCREMENTAL /ILK:"cmTC_c518d.dir\\Debug\\cmTC_c518d.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-gs5ecc/Debug/cmTC_c518d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-gs5ecc/Debug/cmTC_c518d.lib" /MACHINE:X64  /machine:x64 cmTC_c518d.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 无法打开文件“pthreads.lib” [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\cmTC_c518d.vcxproj]
        已完成生成项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\cmTC_c518d.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\cmTC_c518d.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : fatal error LNK1104: 无法打开文件“pthreads.lib” [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gs5ecc\\cmTC_c518d.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.28
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:56 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-rtyf8s"
      binary: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-rtyf8s"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /fp:fast"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-rtyf8s'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_ff290.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/1 18:53:52。
        节点 1 上的项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\cmTC_ff290.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_ff290.dir\\Debug\\”。
          正在创建目录“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\Debug\\”。
          正在创建目录“cmTC_ff290.dir\\Debug\\cmTC_ff290.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_ff290.dir\\Debug\\cmTC_ff290.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_ff290.dir\\Debug\\\\" /Fd"cmTC_ff290.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\CheckFunctionExists.cxx"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CheckFunctionExists.cxx
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_ff290.dir\\Debug\\\\" /Fd"cmTC_ff290.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\CheckFunctionExists.cxx"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\Debug\\cmTC_ff290.exe" /INCREMENTAL /ILK:"cmTC_ff290.dir\\Debug\\cmTC_ff290.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-rtyf8s/Debug/cmTC_ff290.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-rtyf8s/Debug/cmTC_ff290.lib" /MACHINE:X64  /machine:x64 cmTC_ff290.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 无法打开文件“pthread.lib” [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\cmTC_ff290.vcxproj]
        已完成生成项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\cmTC_ff290.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\cmTC_ff290.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : fatal error LNK1104: 无法打开文件“pthread.lib” [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rtyf8s\\cmTC_ff290.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.28
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:36 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-6ijy99"
      binary: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-6ijy99"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /Oi /Ot /Oy /GL /arch:AVX2 /fp:fast /bigobj /EHsc /MP /favor:INTEL64"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /LTCG /OPT:REF /OPT:ICF"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_openmp"
      cached: true
      stdout: |
        Change Dir: 'E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-6ijy99'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_adef6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/1 19:41:08。
        节点 1 上的项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\cmTC_adef6.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_adef6.dir\\Debug\\”。
          正在创建目录“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\Debug\\”。
          正在创建目录“cmTC_adef6.dir\\Debug\\cmTC_adef6.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_adef6.dir\\Debug\\cmTC_adef6.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /MP /Od /Ob0 /Oi /Ot /Oy /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_adef6.dir\\Debug\\\\" /Fd"cmTC_adef6.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj /favor:INTEL64 "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\OpenMPTryFlag.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          OpenMPTryFlag.cpp
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /MP /Od /Ob0 /Oi /Ot /Oy /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_adef6.dir\\Debug\\\\" /Fd"cmTC_adef6.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj /favor:INTEL64 "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\OpenMPTryFlag.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\Debug\\cmTC_adef6.exe" /INCREMENTAL /ILK:"cmTC_adef6.dir\\Debug\\cmTC_adef6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-6ijy99/Debug/cmTC_adef6.pdb" /SUBSYSTEM:CONSOLE /OPT:REF /OPT:ICF /LTCG /LTCGOUT:"cmTC_adef6.dir\\Debug\\cmTC_adef6.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-6ijy99/Debug/cmTC_adef6.lib" /MACHINE:X64  /machine:x64 cmTC_adef6.dir\\Debug\\OpenMPTryFlag.obj
        LINK : warning LNK4075: 忽略“/INCREMENTAL”(由于“/LTCG”规范) [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\cmTC_adef6.vcxproj]
          正在生成代码
          已完成代码的生成
          cmTC_adef6.vcxproj -> E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\Debug\\cmTC_adef6.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_adef6.dir\\Debug\\cmTC_adef6.tlog\\unsuccessfulbuild”。
          正在对“cmTC_adef6.dir\\Debug\\cmTC_adef6.tlog\\cmTC_adef6.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\cmTC_adef6.vcxproj”(默认目标)的操作。
        
        已成功生成。
        
        “E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\cmTC_adef6.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : warning LNK4075: 忽略“/INCREMENTAL”(由于“/LTCG”规范) [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6ijy99\\cmTC_adef6.vcxproj]
        
            1 个警告
            0 个错误
        
        已用时间 00:00:00.44
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:456 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindOpenMP.cmake:596 (_OPENMP_GET_SPEC_DATE)"
      - "CMakeLists.txt:36 (find_package)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-y68v48"
      binary: "E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-y68v48"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /Oi /Ot /Oy /GL /arch:AVX2 /fp:fast /bigobj /EHsc /MP /favor:INTEL64"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /LTCG /OPT:REF /OPT:ICF"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-y68v48'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_7d5be.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/1 19:41:09。
        节点 1 上的项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\cmTC_7d5be.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_7d5be.dir\\Debug\\”。
          正在创建目录“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\Debug\\”。
          正在创建目录“cmTC_7d5be.dir\\Debug\\cmTC_7d5be.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_7d5be.dir\\Debug\\cmTC_7d5be.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /MP /Od /Ob0 /Oi /Ot /Oy /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_7d5be.dir\\Debug\\\\" /Fd"cmTC_7d5be.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj /favor:INTEL64 "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\OpenMPCheckVersion.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          OpenMPCheckVersion.cpp
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /MP /Od /Ob0 /Oi /Ot /Oy /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++17 /Fo"cmTC_7d5be.dir\\Debug\\\\" /Fd"cmTC_7d5be.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj /favor:INTEL64 "E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\OpenMPCheckVersion.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\Debug\\cmTC_7d5be.exe" /INCREMENTAL /ILK:"cmTC_7d5be.dir\\Debug\\cmTC_7d5be.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-y68v48/Debug/cmTC_7d5be.pdb" /SUBSYSTEM:CONSOLE /OPT:REF /OPT:ICF /LTCG /LTCGOUT:"cmTC_7d5be.dir\\Debug\\cmTC_7d5be.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/an/phi-giant/cpp/build/CMakeFiles/CMakeScratch/TryCompile-y68v48/Debug/cmTC_7d5be.lib" /MACHINE:X64  /machine:x64 cmTC_7d5be.dir\\Debug\\OpenMPCheckVersion.obj
        LINK : warning LNK4075: 忽略“/INCREMENTAL”(由于“/LTCG”规范) [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\cmTC_7d5be.vcxproj]
          正在生成代码
          已完成代码的生成
          cmTC_7d5be.vcxproj -> E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\Debug\\cmTC_7d5be.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_7d5be.dir\\Debug\\cmTC_7d5be.tlog\\unsuccessfulbuild”。
          正在对“cmTC_7d5be.dir\\Debug\\cmTC_7d5be.tlog\\cmTC_7d5be.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\cmTC_7d5be.vcxproj”(默认目标)的操作。
        
        已成功生成。
        
        “E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\cmTC_7d5be.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : warning LNK4075: 忽略“/INCREMENTAL”(由于“/LTCG”规范) [E:\\an\\phi-giant\\cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y68v48\\cmTC_7d5be.vcxproj]
        
            1 个警告
            0 个错误
        
        已用时间 00:00:00.34
        
      exitCode: 0
...
