@echo off
REM Super Engineer CUDA Independent Compilation Script
echo Setting up Visual Studio environment...
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

echo Compiling CUDA kernels...
nvcc -O3 --use_fast_math --extended-lambda --expt-relaxed-constexpr -std=c++17 -arch=sm_75 -c phi_cuda.cu -o phi_cuda_compiled.obj

if %ERRORLEVEL% EQU 0 (
    echo CUDA compilation successful!
    echo Output: phi_cuda_compiled.obj
) else (
    echo CUDA compilation failed!
    exit /b 1
)

echo Testing CUDA runtime...
nvcc --version

echo CUDA independent compilation completed!
